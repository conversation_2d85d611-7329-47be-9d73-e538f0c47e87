#!/usr/bin/env python3
"""
Simple test script to check if the Streamlit app can be imported and basic functions work
"""

import sys
import os

# Add current directory to path
sys.path.append(".")


def test_imports():
    """Test if all required modules can be imported"""
    try:
        import streamlit as st

        print("✅ Streamlit imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import Streamlit: {e}")
        return False

    try:
        # Test if we can at least import the file without running Streamlit config
        import importlib.util

        spec = importlib.util.spec_from_file_location(
            "streamlit_app", "streamlit_app.py"
        )
        if spec is None:
            print("❌ Could not load streamlit_app.py")
            return False
        print("✅ Streamlit app file can be loaded")
    except Exception as e:
        print(f"❌ Failed to load app file: {e}")
        return False

    return True


def test_basic_functions():
    """Test basic functions"""
    try:
        from streamlit_app import (
            check_existing_files,
            get_latest_json_file,
            check_environment,
        )

        # Test check_existing_files
        result = check_existing_files("29507397")
        print(f"✅ check_existing_files works: {result}")

        # Test get_latest_json_file
        json_file = get_latest_json_file("29507397")
        print(f"✅ get_latest_json_file works: {json_file}")

        # Test check_environment
        missing_vars = check_environment()
        print(f"✅ check_environment works: missing vars = {missing_vars}")

        return True
    except Exception as e:
        print(f"❌ Error testing functions: {e}")
        return False


def test_directory_structure():
    """Test if required directories exist"""
    required_dirs = ["output", "data/input/markdown", "src/utils", "src/core/crews"]

    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✅ Directory exists: {dir_path}")
        else:
            print(f"⚠️ Directory missing: {dir_path}")


def main():
    print("🧪 Testing Streamlit App Setup")
    print("=" * 50)

    # Test imports
    print("\n📦 Testing imports...")
    if not test_imports():
        print("❌ Import tests failed")
        return

    # Test directory structure
    print("\n📁 Testing directory structure...")
    test_directory_structure()

    # Test basic functions
    print("\n🔧 Testing basic functions...")
    if test_basic_functions():
        print("\n✅ All tests passed! The app should work correctly.")
    else:
        print("\n❌ Some function tests failed.")

    print("\n🚀 To run the app, use:")
    print("streamlit run streamlit_app.py")


if __name__ == "__main__":
    main()
