# PMID Processing and Validation Streamlit App

这是一个用于处理PMID（PubMed ID）和验证生成数据的Streamlit Web应用程序。

## 功能特性

### 1. PMID处理页面
- **输入PMID**: 用户可以输入要处理的PMID
- **文件检查**: 自动检查`output`目录中是否已存在对应的MD和JSON文件
- **PDF上传**: 如果文件不存在，提供PDF上传接口
- **自动处理**: 
  - 使用Mineru API将PDF转换为Markdown
  - 执行CrewAI流程生成结构化JSON数据

### 2. 数据验证页面
- **JSON数据展示**: 加载并展示对应PMID的JSON文件内容
- **字段验证**: 为每个数据字段提供文本框展示和准确性验证选项
- **验证提交**: 保存验证结果到文件

## 安装和运行

### 1. 安装依赖
```bash
pip install -r requirements_streamlit.txt
```

### 2. 环境变量设置
确保在`.env`文件中设置了以下环境变量：
```
MINERU_API=your_mineru_api_token
PROVIDER=your_provider
MODEL=your_model_name
PROVIDER_API_KEY=your_api_key
PROVIDER_BASE_URL=your_base_url
```

### 3. 运行应用
```bash
# 方法1: 直接运行
streamlit run streamlit_app.py

# 方法2: 使用脚本
chmod +x run_streamlit.sh
./run_streamlit.sh
```

应用将在 `http://localhost:8501` 启动。

## 使用说明

### PMID处理流程

1. **选择"PMID Processing"页面**
2. **输入PMID**: 在文本框中输入要处理的PMID（例如：29507397）
3. **检查文件状态**: 系统会自动检查是否已存在相关文件
4. **上传PDF**（如果需要）:
   - 如果没有找到MD或JSON文件，会显示PDF上传界面
   - 选择对应的PDF文件并上传
   - 点击"Process PDF"按钮开始处理
5. **等待处理完成**: 
   - 系统会先将PDF转换为Markdown
   - 然后运行CrewAI流程生成JSON数据

### 数据验证流程

1. **选择"Data Validation"页面**
2. **输入PMID**: 输入要验证的PMID
3. **查看数据**: 系统会加载最新的JSON文件并展示蛋白质数据
4. **验证字段**: 
   - 每个蛋白质的数据字段都会以文本框形式展示
   - 右侧提供复选框来标记字段是否准确
5. **提交验证**: 点击"Submit Validation"保存验证结果

## 数据结构

应用处理的JSON数据包含以下主要字段：

- `gene_name`: 基因名称
- `organism`: 生物体
- `mlo`: 无膜细胞器
- `mlo_association_summary`: MLO关联摘要
- `location`: 位置
- `observed_ps_behavior_summary`: 观察到的相分离行为摘要
- `key_protein_regions_studied_ps`: 研究的关键蛋白区域
- `experiment_types`: 实验类型
- `material_state`: 材料状态
- `overall_material_properties_summary`: 整体材料属性摘要
- `functional_implications_summary`: 功能意义摘要
- `class_`: 分类

## 文件结构

```
├── streamlit_app.py              # 主应用文件
├── requirements_streamlit.txt    # Python依赖
├── run_streamlit.sh             # 启动脚本
├── README_streamlit.md          # 说明文档
├── output/                      # 输出目录
│   ├── {pmid}_*.json           # 生成的JSON文件
│   ├── {pmid}_*.md             # 生成的Markdown文件
│   └── validation_*.json       # 验证结果文件
└── data/input/markdown/         # Markdown输入目录
    └── {pmid}/
        └── {pmid}.md           # 转换后的Markdown文件
```

## 注意事项

1. **API Token**: 确保Mineru API token有效且有足够的配额
2. **文件权限**: 确保应用有权限读写`output`和`data`目录
3. **网络连接**: PDF转换和CrewAI处理需要网络连接
4. **处理时间**: 根据PDF大小和复杂度，处理时间可能较长

## 故障排除

### 常见问题

1. **导入错误**: 确保所有依赖都已正确安装
2. **API错误**: 检查环境变量设置和API token有效性
3. **文件路径错误**: 确保相对路径正确，建议从项目根目录运行
4. **权限错误**: 确保有足够的文件系统权限

### 调试模式

可以在Streamlit应用中添加调试信息：
```python
st.write("Debug info:", some_variable)
```

## 扩展功能

可以考虑添加的功能：
- 批量处理多个PMID
- 导出验证结果为Excel格式
- 添加数据统计和可视化
- 集成更多的验证规则
- 添加用户认证和权限管理
