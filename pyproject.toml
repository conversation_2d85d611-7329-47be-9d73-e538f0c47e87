[project]
name = "phasepdb"
version = "0.1.0"
description = "phasepdb using crewAI"
authors = [{ name = "Your Name", email = "<EMAIL>" }]
requires-python = ">=3.10,<3.13"
dependencies = [
    "crewai[tools]>=0.102.0,<0.130.0",
]

[project.scripts]
kickoff = "phasepdb.main:kickoff"
plot = "phasepdb.main:plot"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.crewai]
type = "flow"
