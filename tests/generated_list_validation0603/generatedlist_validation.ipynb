{"cells": [{"cell_type": "code", "execution_count": 1, "id": "2c94669c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "#import seaborn as sns\n", "import os,re\n", "import random\n", "import scipy.stats as stats\n", "import pickle\n", "import math\n", "import networkx as nx\n", "from sklearn import decomposition\n", "#from pyecharts import options as opts\n", "#from pyecharts.charts import Sankey\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "import copy\n", "from sklearn import preprocessing\n", "from sklearn.cluster import KMeans\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.preprocessing import MinMaxScaler"]}, {"cell_type": "markdown", "id": "1e3060f2", "metadata": {}, "source": ["v2.0表格"]}, {"cell_type": "code", "execution_count": 5, "id": "50401052", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PSID</th>\n", "      <th>curator</th>\n", "      <th>PMID</th>\n", "      <th>organism</th>\n", "      <th>cellline_tissue</th>\n", "      <th>uniprot_entry</th>\n", "      <th>gene_name</th>\n", "      <th>location</th>\n", "      <th>MLO</th>\n", "      <th>diagram</th>\n", "      <th>...</th>\n", "      <th>other_note</th>\n", "      <th>PTM</th>\n", "      <th>PTM_note</th>\n", "      <th>mutation</th>\n", "      <th>mutation_note</th>\n", "      <th>domain</th>\n", "      <th>domain_note</th>\n", "      <th>splicing</th>\n", "      <th>repeat</th>\n", "      <th>oligomerization</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>psself318</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>33479219</td>\n", "      <td>Severe acute respiratory syndrome coronavirus 2</td>\n", "      <td>HeLa cells</td>\n", "      <td>P0DTC9</td>\n", "      <td>N</td>\n", "      <td>Cytoplasm</td>\n", "      <td>SARS-CoV-2 N condensate</td>\n", "      <td>33479219-P0DTC9-1,33479219-P0DTC9-2,33479219-P...</td>\n", "      <td>...</td>\n", "      <td>_</td>\n", "      <td>phosphorylation</td>\n", "      <td>phosphorylation may inhibit N protein LLPS.</td>\n", "      <td>p.<PERSON>_<PERSON>,p.T176_S206del, p.T176_V246del</td>\n", "      <td>Subsequent studies showed that removal of N-te...</td>\n", "      <td>N-terminal intrinsically disordered region(IDR...</td>\n", "      <td>N-terminal IDR is necessary for SARS-CoV-2 N p...</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>psself317</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>33479198</td>\n", "      <td>Severe acute respiratory syndrome coronavirus 2</td>\n", "      <td>U2OS cells</td>\n", "      <td>P0DTC9</td>\n", "      <td>N</td>\n", "      <td>Cytoplasm</td>\n", "      <td>SARS-CoV-2 N condensate</td>\n", "      <td>_</td>\n", "      <td>...</td>\n", "      <td>_</td>\n", "      <td>phosphorylation</td>\n", "      <td>phosphorylation reduces the viscosity of N + R...</td>\n", "      <td>ΔN-terminal IDR,ΔC-terminal IDR,ΔL/Q-rich regi...</td>\n", "      <td>deletion of the N-terminal IDR and/or C-termin...</td>\n", "      <td>central IDR(aa 175–246),S/R-rich region(aa 175...</td>\n", "      <td>the central IDR is critical for SARS-CoV-2 N p...</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>psother48</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>27212236</td>\n", "      <td>Homo sapiens</td>\n", "      <td>X. laevis oocytes</td>\n", "      <td>P06748</td>\n", "      <td>NPM1</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Nucleolus</td>\n", "      <td>27212236-P06748-1,27212236-P06748-2</td>\n", "      <td>...</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>ΔC,ΔN</td>\n", "      <td>We deleted the N-terminal oligomerization doma...</td>\n", "      <td>N-terminal oligomerization domain(OD),C-termin...</td>\n", "      <td>the OD of NPM1 should generate the requisite m...</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>Phase separation of NPM1 requires the oligomer...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>psother41</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>26836305</td>\n", "      <td>Homo sapiens</td>\n", "      <td>MEF cells</td>\n", "      <td>P06748</td>\n", "      <td>NPM1</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Nucleolus</td>\n", "      <td>26836305-P06748-1</td>\n", "      <td>...</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>ΔNBD,ΔN</td>\n", "      <td>N240(ΔNBD), but not the ΔN construct phase sep...</td>\n", "      <td>oligomerization domain(OD),nucleic acid bindin...</td>\n", "      <td>the oligomerization domain (OD), containing th...</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>we found that disruption of its multi-modal in...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>psother366</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>32487073</td>\n", "      <td>Homo sapiens</td>\n", "      <td>U2OS cells</td>\n", "      <td>P04150</td>\n", "      <td>GR</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>...</td>\n", "      <td>GR foci formation requires association with ch...</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>p.P481R,p.A465T,p.I634A</td>\n", "      <td>A constitutively tetrameric GR mutant (GR P481...</td>\n", "      <td>N-terminal domain (NTD),C-terminal ligand-bind...</td>\n", "      <td>The GR NTD probably plays a role in establishi...</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>A constitutively tetrameric GR mutant (GRP481R...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>956</th>\n", "      <td>psother415</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>32963000</td>\n", "      <td>Human respiratory syncytial virus A</td>\n", "      <td>BHK-21 cells</td>\n", "      <td>P03421</td>\n", "      <td>P</td>\n", "      <td>_</td>\n", "      <td>Inclusion body</td>\n", "      <td>_</td>\n", "      <td>...</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>oligomerization domain (OD)</td>\n", "      <td>The fusion of the OD to the C-terminal residue...</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>Both the oligomerization domain and the C-term...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>957</th>\n", "      <td>psother408</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>32878896</td>\n", "      <td>Human respiratory syncytial virus A</td>\n", "      <td>Vero cells</td>\n", "      <td>P03421</td>\n", "      <td>P</td>\n", "      <td>_</td>\n", "      <td>Inclusion body</td>\n", "      <td>_</td>\n", "      <td>...</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "    </tr>\n", "    <tr>\n", "      <th>958</th>\n", "      <td>psother148</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>30826453,30181255</td>\n", "      <td>Vesicular stomatitis Indiana virus</td>\n", "      <td>Vero cells</td>\n", "      <td>P03520</td>\n", "      <td>P</td>\n", "      <td>Cytoplasm</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>...</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "    </tr>\n", "    <tr>\n", "      <th>959</th>\n", "      <td>psself193</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>32169085</td>\n", "      <td>Saccharomyces cerevisiae</td>\n", "      <td>Yeast</td>\n", "      <td>P38713</td>\n", "      <td>Osh3</td>\n", "      <td>Cytoplasm</td>\n", "      <td>Stress granule</td>\n", "      <td>_</td>\n", "      <td>...</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>GOLD domain,ORD domain</td>\n", "      <td>The Osh3 protein displays heat sensitivity in ...</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "    </tr>\n", "    <tr>\n", "      <th>960</th>\n", "      <td>psself148</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>31267591</td>\n", "      <td>Homo sapiens</td>\n", "      <td>U2OS cells</td>\n", "      <td>Q12888</td>\n", "      <td>53BP1</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>DNA damage foci</td>\n", "      <td>_</td>\n", "      <td>...</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>p.W1495A</td>\n", "      <td>The W1495A TTD mutation abolished foci formati...</td>\n", "      <td>C‐terminus,largely unstructured N‐terminus</td>\n", "      <td>the C‐terminus, comprising amino acids 1140–19...</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "      <td>_</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>961 rows × 33 columns</p>\n", "</div>"], "text/plain": ["           PSID      curator               PMID  \\\n", "0     psself318  <PERSON><PERSON><PERSON>           33479219   \n", "1     psself317  <PERSON><PERSON><PERSON>           33479198   \n", "2     psother48  <PERSON><PERSON><PERSON>           27212236   \n", "3     psother41  <PERSON><PERSON><PERSON>           26836305   \n", "4    psother366   Haotai Xie           32487073   \n", "..          ...          ...                ...   \n", "956  psother415   Haotai Xie           32963000   \n", "957  psother408   Haotai Xie           32878896   \n", "958  psother148  <PERSON><PERSON><PERSON>  30826453,30181255   \n", "959   psself193   Haotai Xie           32169085   \n", "960   psself148   Haotai Xie           31267591   \n", "\n", "                                            organism    cellline_tissue  \\\n", "0    Severe acute respiratory syndrome coronavirus 2         HeLa cells   \n", "1    Severe acute respiratory syndrome coronavirus 2         U2OS cells   \n", "2                                       Homo sapiens  X. laevis oocytes   \n", "3                                       Homo sapiens          MEF cells   \n", "4                                       Homo sapiens         U2OS cells   \n", "..                                               ...                ...   \n", "956              Human respiratory syncytial virus A       BHK-21 cells   \n", "957              Human respiratory syncytial virus A         Vero cells   \n", "958               Vesicular stomatitis Indiana virus         Vero cells   \n", "959                         Saccharomyces cerevisiae              Yeast   \n", "960                                     Homo sapiens         U2OS cells   \n", "\n", "    uniprot_entry gene_name   location                      MLO  \\\n", "0          P0DTC9         N  Cytoplasm  SARS-CoV-2 N condensate   \n", "1          P0DTC9         N  Cytoplasm  SARS-CoV-2 N condensate   \n", "2          P06748      NPM1    Nucleus                Nucleolus   \n", "3          P06748      NPM1    Nucleus                Nucleolus   \n", "4          P04150        GR    Nucleus                        _   \n", "..            ...       ...        ...                      ...   \n", "956        P03421         P          _           Inclusion body   \n", "957        P03421         P          _           Inclusion body   \n", "958        P03520         P  Cytoplasm                        _   \n", "959        P38713      Osh3  Cytoplasm           Stress granule   \n", "960        Q12888     53BP1    Nucleus          DNA damage foci   \n", "\n", "                                               diagram  ...  \\\n", "0    33479219-P0DTC9-1,33479219-P0DTC9-2,33479219-P...  ...   \n", "1                                                    _  ...   \n", "2                  27212236-P06748-1,27212236-P06748-2  ...   \n", "3                                    26836305-P06748-1  ...   \n", "4                                                    _  ...   \n", "..                                                 ...  ...   \n", "956                                                  _  ...   \n", "957                                                  _  ...   \n", "958                                                  _  ...   \n", "959                                                  _  ...   \n", "960                                                  _  ...   \n", "\n", "                                            other_note              PTM  \\\n", "0                                                    _  phosphorylation   \n", "1                                                    _  phosphorylation   \n", "2                                                    _                _   \n", "3                                                    _                _   \n", "4    GR foci formation requires association with ch...                _   \n", "..                                                 ...              ...   \n", "956                                                  _                _   \n", "957                                                  _                _   \n", "958                                                  _                _   \n", "959                                                  _                _   \n", "960                                                  _                _   \n", "\n", "                                              PTM_note  \\\n", "0          phosphorylation may inhibit N protein LLPS.   \n", "1    phosphorylation reduces the viscosity of N + R...   \n", "2                                                    _   \n", "3                                                    _   \n", "4                                                    _   \n", "..                                                 ...   \n", "956                                                  _   \n", "957                                                  _   \n", "958                                                  _   \n", "959                                                  _   \n", "960                                                  _   \n", "\n", "                                              mutation  \\\n", "0           p.<PERSON>_<PERSON><PERSON><PERSON>,p.T176_S206del, p.T176_V246del   \n", "1    ΔN-terminal IDR,ΔC-terminal IDR,ΔL/Q-rich regi...   \n", "2                                                ΔC,ΔN   \n", "3                                              ΔNBD,ΔN   \n", "4                              p.P481R,p.A465T,p.I634A   \n", "..                                                 ...   \n", "956                                                  _   \n", "957                                                  _   \n", "958                                                  _   \n", "959                                                  _   \n", "960                                           p.W1495A   \n", "\n", "                                         mutation_note  \\\n", "0    Subsequent studies showed that removal of N-te...   \n", "1    deletion of the N-terminal IDR and/or C-termin...   \n", "2    We deleted the N-terminal oligomerization doma...   \n", "3    N240(ΔNBD), but not the ΔN construct phase sep...   \n", "4    A constitutively tetrameric GR mutant (GR P481...   \n", "..                                                 ...   \n", "956                                                  _   \n", "957                                                  _   \n", "958                                                  _   \n", "959                                                  _   \n", "960  The W1495A TTD mutation abolished foci formati...   \n", "\n", "                                                domain  \\\n", "0    N-terminal intrinsically disordered region(IDR...   \n", "1    central IDR(aa 175–246),S/R-rich region(aa 175...   \n", "2    N-terminal oligomerization domain(OD),C-termin...   \n", "3    oligomerization domain(OD),nucleic acid bindin...   \n", "4    N-terminal domain (NTD),C-terminal ligand-bind...   \n", "..                                                 ...   \n", "956                        oligomerization domain (OD)   \n", "957                                                  _   \n", "958                                                  _   \n", "959                             GOLD domain,ORD domain   \n", "960         C‐terminus,largely unstructured N‐terminus   \n", "\n", "                                           domain_note splicing repeat  \\\n", "0    N-terminal IDR is necessary for SARS-CoV-2 N p...        _      _   \n", "1    the central IDR is critical for SARS-CoV-2 N p...        _      _   \n", "2    the OD of NPM1 should generate the requisite m...        _      _   \n", "3    the oligomerization domain (OD), containing th...        _      _   \n", "4    The GR NTD probably plays a role in establishi...        _      _   \n", "..                                                 ...      ...    ...   \n", "956  The fusion of the OD to the C-terminal residue...        _      _   \n", "957                                                  _        _      _   \n", "958                                                  _        _      _   \n", "959  The Osh3 protein displays heat sensitivity in ...        _      _   \n", "960  the C‐terminus, comprising amino acids 1140–19...        _      _   \n", "\n", "                                       oligomerization  \n", "0                                                    _  \n", "1                                                    _  \n", "2    Phase separation of NPM1 requires the oligomer...  \n", "3    we found that disruption of its multi-modal in...  \n", "4    A constitutively tetrameric GR mutant (GRP481R...  \n", "..                                                 ...  \n", "956  Both the oligomerization domain and the C-term...  \n", "957                                                  _  \n", "958                                                  _  \n", "959                                                  _  \n", "960                                                  _  \n", "\n", "[961 rows x 33 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["lis = pd.read_excel('/home/<USER>/PhasesepDBV3/phasepdbv2_llps.xlsx')\n", "lis"]}, {"cell_type": "code", "execution_count": 7, "id": "a4d6e258", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['PSID', 'curator', 'PMID', 'organism', 'cellline_tissue',\n", "       'uniprot_entry', 'gene_name', 'location', 'MLO', 'diagram',\n", "       'diagram_note', 'experiment', 'publication_note', 'material_state',\n", "       'material_state_note', 'class', 'region', 'partner', 'partner_uniprot',\n", "       'partner_note', 'RNA', 'RNA_note', 'other', 'other_note', 'PTM',\n", "       'PTM_note', 'mutation', 'mutation_note', 'domain', 'domain_note',\n", "       'splicing', 'repeat', 'oligomerization'],\n", "      dtype='object')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["lis.columns"]}, {"cell_type": "code", "execution_count": null, "id": "65db78c2", "metadata": {}, "outputs": [], "source": ["lis_1 = lis[['PMID', 'organism', 'gene_name', 'location', 'MLO', 'experiment',\n", "       'class', 'material_state']]"]}, {"cell_type": "code", "execution_count": 25, "id": "1debeb3f", "metadata": {"collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理前:\n", "行数: 961, PMID数: 444\n", "0    33479219\n", "1    33479198\n", "2    27212236\n", "3    26836305\n", "4    32487073\n", "Name: PMID, dtype: object\n", "\n", "处理后:\n", "行数: 1216, PMID数: 495\n", "       PMID                                         organism gene_name  \\\n", "0  33479219  Severe acute respiratory syndrome coronavirus 2         N   \n", "1  33479198  Severe acute respiratory syndrome coronavirus 2         N   \n", "2  27212236                                     Homo sapiens      NPM1   \n", "3  26836305                                     Homo sapiens      NPM1   \n", "4  32487073                                     Homo sapiens        GR   \n", "\n", "    location                      MLO  \\\n", "0  Cytoplasm  SARS-CoV-2 N condensate   \n", "1  Cytoplasm  SARS-CoV-2 N condensate   \n", "2    Nucleus                Nucleolus   \n", "3    Nucleus                Nucleolus   \n", "4    Nucleus                        _   \n", "\n", "                                          experiment     class  \\\n", "0  in vitro droplet formation,in vivo droplet for...   PS-self   \n", "1  in vitro droplet formation,in vitro FRAP,in vi...   PS-self   \n", "2  in vitro droplet formation,in vitro FRAP,in vi...  PS-other   \n", "3  in vitro droplet formation,in vivo droplet for...  PS-other   \n", "4             in vivo droplet formation,in vivo FRAP  PS-other   \n", "\n", "          material_state  \n", "0  liquid,hydrogel,solid  \n", "1        liquid,hydrogel  \n", "2                 liquid  \n", "3                 liquid  \n", "4                 liquid  \n"]}], "source": ["import pandas as pd\n", "\n", "def expand_pmid_column(df):\n", "    \"\"\"\n", "    处理包含多个PMID的列，将单个行拆分为多个行（每个PMID一行）\n", "    \n", "    参数:\n", "        df (pd.DataFrame): 包含PMID列的DataFrame\n", "        \n", "    返回:\n", "        pd.DataFrame: 处理后的新DataFrame\n", "    \"\"\"\n", "    # 复制原始DataFrame避免修改原数据\n", "    expanded_df = df.copy()\n", "    \n", "    # 清洗PMID列：转换为字符串，去除空格，替换可能的换行符和分号\n", "    expanded_df['PMID'] = expanded_df['PMID'].astype(str).str.strip().str.replace(r'[\\n;]', ',', regex=True)\n", "    \n", "    # 将逗号分隔的多个PMID拆分为列表\n", "    expanded_df['PMID'] = expanded_df['PMID'].str.split(',')\n", "    \n", "    # 使用explode方法将每个PMID拆分为单独的行\n", "    expanded_df = expanded_df.explode('PMID', ignore_index=True)\n", "    \n", "    # 去除PMID中的任何空格\n", "    expanded_df['PMID'] = expanded_df['PMID'].str.strip()\n", "    \n", "    # 移除空PMID行\n", "    expanded_df = expanded_df[expanded_df['PMID'] != '']\n", "    \n", "    # 转换PMID为整数类型\n", "    # 注意：使用errors='coerce'忽略无法转换的值\n", "    expanded_df['PMID'] = pd.to_numeric(expanded_df['PMID'], errors='coerce').dropna()\n", "    expanded_df['PMID'] = expanded_df['PMID'].astype(int)\n", "    \n", "    return expanded_df\n", "\n", "# 使用示例\n", "processed_lis_1 = expand_pmid_column(lis_1)\n", "\n", "# 查看处理前后的区别\n", "print(\"处理前:\")\n", "print(f\"行数: {lis_1.shape[0]}, PMID数: {lis_1['PMID'].nunique()}\")\n", "print(lis_1['PMID'].head())\n", "\n", "print(\"\\n处理后:\")\n", "print(f\"行数: {processed_lis_1.shape[0]}, PMID数: {processed_lis_1['PMID'].nunique()}\")\n", "print(processed_lis_1.head())"]}, {"cell_type": "code", "execution_count": 26, "id": "caadbbbb", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PMID</th>\n", "      <th>organism</th>\n", "      <th>gene_name</th>\n", "      <th>location</th>\n", "      <th>MLO</th>\n", "      <th>experiment</th>\n", "      <th>class</th>\n", "      <th>material_state</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>33479219</td>\n", "      <td>Severe acute respiratory syndrome coronavirus 2</td>\n", "      <td>N</td>\n", "      <td>Cytoplasm</td>\n", "      <td>SARS-CoV-2 N condensate</td>\n", "      <td>in vitro droplet formation,in vivo droplet for...</td>\n", "      <td>PS-self</td>\n", "      <td>liquid,hydrogel,solid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>33479198</td>\n", "      <td>Severe acute respiratory syndrome coronavirus 2</td>\n", "      <td>N</td>\n", "      <td>Cytoplasm</td>\n", "      <td>SARS-CoV-2 N condensate</td>\n", "      <td>in vitro droplet formation,in vitro FRAP,in vi...</td>\n", "      <td>PS-self</td>\n", "      <td>liquid,hydrogel</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>27212236</td>\n", "      <td>Homo sapiens</td>\n", "      <td>NPM1</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Nucleolus</td>\n", "      <td>in vitro droplet formation,in vitro FRAP,in vi...</td>\n", "      <td>PS-other</td>\n", "      <td>liquid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>26836305</td>\n", "      <td>Homo sapiens</td>\n", "      <td>NPM1</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Nucleolus</td>\n", "      <td>in vitro droplet formation,in vivo droplet for...</td>\n", "      <td>PS-other</td>\n", "      <td>liquid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>32487073</td>\n", "      <td>Homo sapiens</td>\n", "      <td>GR</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>_</td>\n", "      <td>in vivo droplet formation,in vivo FRAP</td>\n", "      <td>PS-other</td>\n", "      <td>liquid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1211</th>\n", "      <td>32878896</td>\n", "      <td>Human respiratory syncytial virus A</td>\n", "      <td>P</td>\n", "      <td>_</td>\n", "      <td>Inclusion body</td>\n", "      <td>in vivo droplet formation,in vivo FRAP</td>\n", "      <td>PS-other</td>\n", "      <td>liquid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1212</th>\n", "      <td>30826453</td>\n", "      <td>Vesicular stomatitis Indiana virus</td>\n", "      <td>P</td>\n", "      <td>Cytoplasm</td>\n", "      <td>_</td>\n", "      <td>in vivo droplet formation,in vivo FRAP</td>\n", "      <td>PS-other</td>\n", "      <td>liquid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1213</th>\n", "      <td>30181255</td>\n", "      <td>Vesicular stomatitis Indiana virus</td>\n", "      <td>P</td>\n", "      <td>Cytoplasm</td>\n", "      <td>_</td>\n", "      <td>in vivo droplet formation,in vivo FRAP</td>\n", "      <td>PS-other</td>\n", "      <td>liquid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1214</th>\n", "      <td>32169085</td>\n", "      <td>Saccharomyces cerevisiae</td>\n", "      <td>Osh3</td>\n", "      <td>Cytoplasm</td>\n", "      <td>Stress granule</td>\n", "      <td>in vitro droplet formation,in vivo droplet for...</td>\n", "      <td>PS-self</td>\n", "      <td>hydrogel,solid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1215</th>\n", "      <td>31267591</td>\n", "      <td>Homo sapiens</td>\n", "      <td>53BP1</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>DNA damage foci</td>\n", "      <td>in vitro droplet formation,in vivo droplet for...</td>\n", "      <td>PS-self</td>\n", "      <td>liquid</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1216 rows × 8 columns</p>\n", "</div>"], "text/plain": ["          PMID                                         organism gene_name  \\\n", "0     33479219  Severe acute respiratory syndrome coronavirus 2         N   \n", "1     33479198  Severe acute respiratory syndrome coronavirus 2         N   \n", "2     27212236                                     Homo sapiens      NPM1   \n", "3     26836305                                     Homo sapiens      NPM1   \n", "4     32487073                                     Homo sapiens        GR   \n", "...        ...                                              ...       ...   \n", "1211  32878896              Human respiratory syncytial virus A         P   \n", "1212  30826453               Vesicular stomatitis Indiana virus         P   \n", "1213  30181255               Vesicular stomatitis Indiana virus         P   \n", "1214  32169085                         Saccharomyces cerevisiae      Osh3   \n", "1215  31267591                                     Homo sapiens     53BP1   \n", "\n", "       location                      MLO  \\\n", "0     Cytoplasm  SARS-CoV-2 N condensate   \n", "1     Cytoplasm  SARS-CoV-2 N condensate   \n", "2       Nucleus                Nucleolus   \n", "3       Nucleus                Nucleolus   \n", "4       Nucleus                        _   \n", "...         ...                      ...   \n", "1211          _           Inclusion body   \n", "1212  Cytoplasm                        _   \n", "1213  Cytoplasm                        _   \n", "1214  Cytoplasm           Stress granule   \n", "1215    Nucleus          DNA damage foci   \n", "\n", "                                             experiment     class  \\\n", "0     in vitro droplet formation,in vivo droplet for...   PS-self   \n", "1     in vitro droplet formation,in vitro FRAP,in vi...   PS-self   \n", "2     in vitro droplet formation,in vitro FRAP,in vi...  PS-other   \n", "3     in vitro droplet formation,in vivo droplet for...  PS-other   \n", "4                in vivo droplet formation,in vivo FRAP  PS-other   \n", "...                                                 ...       ...   \n", "1211             in vivo droplet formation,in vivo FRAP  PS-other   \n", "1212             in vivo droplet formation,in vivo FRAP  PS-other   \n", "1213             in vivo droplet formation,in vivo FRAP  PS-other   \n", "1214  in vitro droplet formation,in vivo droplet for...   PS-self   \n", "1215  in vitro droplet formation,in vivo droplet for...   PS-self   \n", "\n", "             material_state  \n", "0     liquid,hydrogel,solid  \n", "1           liquid,hydrogel  \n", "2                    liquid  \n", "3                    liquid  \n", "4                    liquid  \n", "...                     ...  \n", "1211                 liquid  \n", "1212                 liquid  \n", "1213                 liquid  \n", "1214         hydrogel,solid  \n", "1215                 liquid  \n", "\n", "[1216 rows x 8 columns]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["processed_lis_1"]}, {"cell_type": "markdown", "id": "17d4b4f6", "metadata": {}, "source": ["all-in-one生成表格0603版本"]}, {"cell_type": "code", "execution_count": 11, "id": "48d28d16", "metadata": {"collapsed": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PMID</th>\n", "      <th>gene_name</th>\n", "      <th>organism</th>\n", "      <th>mlo_association_summary</th>\n", "      <th>location</th>\n", "      <th>observed_ps_behavior_summary</th>\n", "      <th>key_protein_regions_studied_ps</th>\n", "      <th>experiment_v2_types</th>\n", "      <th>detailed_experiments_list[0].experiment_type_original</th>\n", "      <th>detailed_experiments_list[0].constructs_used</th>\n", "      <th>...</th>\n", "      <th>entities[1].co_ps_partner_type</th>\n", "      <th>entities[2].gene_name</th>\n", "      <th>entities[2].organism</th>\n", "      <th>entities[2].mlo_location</th>\n", "      <th>entities[2].ps_verification_experiment_type</th>\n", "      <th>entities[2].experimental_conditions_summary</th>\n", "      <th>entities[2].protein_region_involved_ps</th>\n", "      <th>entities[2].droplet_state</th>\n", "      <th>entities[2].droplet_state_transition_observed</th>\n", "      <th>entities[2].co_ps_partner_type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>16263761</td>\n", "      <td>Dvl-2</td>\n", "      <td>Homo sapiens</td>\n", "      <td>Not specified. Cytoplasmic puncta did not colo...</td>\n", "      <td>Cytoplasm</td>\n", "      <td>At high expression levels, Dvl-2 forms non-mem...</td>\n", "      <td>Full-length Dvl-2 and mutants: ΔDIX (deletion ...</td>\n", "      <td>in vivo droplet formation; Other</td>\n", "      <td>Immunofluorescence Microscopy (Puncta Formation)</td>\n", "      <td>Full-length HA-Dvl-2-E<PERSON><PERSON>, HA-Dvl-2-ER</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>16263761</td>\n", "      <td>Dvl-2, <PERSON><PERSON><PERSON><PERSON>-2</td>\n", "      <td>*Homo sapiens* (primary focus). Experiments co...</td>\n", "      <td>Not described in this article. The cytoplasmic...</td>\n", "      <td>NaN</td>\n", "      <td>Dvl-2 forms cytoplasmic puncta exclusively und...</td>\n", "      <td>Full-length HA-tagged Dvl-2 (wild-type). DIX d...</td>\n", "      <td>in vivo droplet formation; Other</td>\n", "      <td>Immunolocalization/Confocal Imaging (Transient...</td>\n", "      <td>HA-Dvl-2-E<PERSON><PERSON> (full-length), HA-Dvl-2-ER (estr...</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>16263762</td>\n", "      <td>Dvl2, Dishevelled 2 (HA-tagged or GFP-tagged c...</td>\n", "      <td>Homo sapiens</td>\n", "      <td>No specific membraneless organelle (MLO) assoc...</td>\n", "      <td>Cytoplasm</td>\n", "      <td>Dvl2 forms cytoplasmic puncta when overexpress...</td>\n", "      <td>Full-length Dvl2. DIX domain (aa 1-114) necess...</td>\n", "      <td>in vivo droplet formation; in vivo FRAP</td>\n", "      <td>Immunofluorescence colocalization with vesicle...</td>\n", "      <td>HA-Dvl2, GFP-Dvl2 (full-length).</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>16263762</td>\n", "      <td>GFP-DIX (DIX domain of Dvl2, aa 1-114, C-termi...</td>\n", "      <td>Homo sapiens</td>\n", "      <td>Not specified. Forms cytoplasmic puncta.</td>\n", "      <td>Cytoplasm</td>\n", "      <td>GFP-DIX forms puncta identical to full-length ...</td>\n", "      <td>Isolated DIX domain (aa 1-114).</td>\n", "      <td>in vivo FRAP</td>\n", "      <td>Photobleaching (FRAP).</td>\n", "      <td>GFP-DIX (isolated domain).</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>16263762</td>\n", "      <td>GFP-Axin (human Axin isoform a, C-terminally t...</td>\n", "      <td>Homo sapiens</td>\n", "      <td>Not specified. Forms cytoplasmic puncta.</td>\n", "      <td>Cytoplasm</td>\n", "      <td>Axin forms puncta morphologically similar to D...</td>\n", "      <td>Full-length Axin (C-terminal GFP tag essential...</td>\n", "      <td>in vivo FRAP</td>\n", "      <td>Photobleaching (FRAP).</td>\n", "      <td>GFP-Axin (full-length).</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1172</th>\n", "      <td>34450138</td>\n", "      <td>Xvelo</td>\n", "      <td><PERSON><PERSON><PERSON> laevis</td>\n", "      <td><PERSON><PERSON><PERSON>i body in oocytes.</td>\n", "      <td>Cytoplasm</td>\n", "      <td>Self-assembles into a solid amyloid aggregate ...</td>\n", "      <td>Self-assembles into amyloid-like structures fo...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1173</th>\n", "      <td>34452345</td>\n", "      <td>Core protein</td>\n", "      <td>Japanese encephalitis virus (JEV), Dengue viru...</td>\n", "      <td>Localizes to nucleoli and stress granules (SGs...</td>\n", "      <td>Nucleus; Cytoplasm</td>\n", "      <td>Forms dynamic condensates via LLPS; localizes ...</td>\n", "      <td>N-terminal intrinsically disordered region (ID...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1174</th>\n", "      <td>34452345</td>\n", "      <td>NS5</td>\n", "      <td>Dengue virus (DENV), Zika virus (ZIKV)</td>\n", "      <td>Localizes to nucleoli (DENV) and centrosomes (...</td>\n", "      <td>Nucleus; Cytoplasm</td>\n", "      <td>Implicated in MLO localization; modulates repl...</td>\n", "      <td>IDR-rich regions; phosphorylation sites in dis...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1175</th>\n", "      <td>34452345</td>\n", "      <td>Nucleoprotein</td>\n", "      <td>Severe acute respiratory syndrome coronavirus ...</td>\n", "      <td>Co-localizes with stress granule marker G3BP.</td>\n", "      <td>Cytoplasm</td>\n", "      <td>Undergoes LLPS with viral RNA; recruits replic...</td>\n", "      <td>Serine/arginine-rich (SR) region; phosphorylat...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1176</th>\n", "      <td>34452345</td>\n", "      <td>Nucleoprotein and Phosphoprotein</td>\n", "      <td>Measles virus (MeV)</td>\n", "      <td>Forms viral replication compartments.</td>\n", "      <td>NaN</td>\n", "      <td>Forms phase-separated compartments with RNA; p...</td>\n", "      <td>Intrinsically disordered regions (IDRs) in bot...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1177 rows × 122 columns</p>\n", "</div>"], "text/plain": ["          PMID                                          gene_name  \\\n", "0     16263761                                              Dvl-2   \n", "1     16263761                               Dvl-2, <PERSON><PERSON><PERSON><PERSON>-2   \n", "2     16263762  Dvl2, Dishevelled 2 (HA-tagged or GFP-tagged c...   \n", "3     16263762  GFP-DIX (DIX domain of Dvl2, aa 1-114, C-termi...   \n", "4     16263762  GFP-Axin (human Axin isoform a, C-terminally t...   \n", "...        ...                                                ...   \n", "1172  34450138                                              Xvelo   \n", "1173  34452345                                       Core protein   \n", "1174  34452345                                                NS5   \n", "1175  34452345                                      Nucleoprotein   \n", "1176  34452345                   Nucleoprotein and Phosphoprotein   \n", "\n", "                                               organism  \\\n", "0                                          Homo sapiens   \n", "1     *Homo sapiens* (primary focus). Experiments co...   \n", "2                                          Homo sapiens   \n", "3                                          Homo sapiens   \n", "4                                          Homo sapiens   \n", "...                                                 ...   \n", "1172                                     <PERSON><PERSON><PERSON> laevis   \n", "1173  Japanese encephalitis virus (JEV), Dengue viru...   \n", "1174             Dengue virus (DENV), Zika virus (ZIKV)   \n", "1175  Severe acute respiratory syndrome coronavirus ...   \n", "1176                                Measles virus (MeV)   \n", "\n", "                                mlo_association_summary            location  \\\n", "0     Not specified. Cytoplasmic puncta did not colo...           Cytoplasm   \n", "1     Not described in this article. The cytoplasmic...                 NaN   \n", "2     No specific membraneless organelle (MLO) assoc...           Cytoplasm   \n", "3              Not specified. Forms cytoplasmic puncta.           Cytoplasm   \n", "4              Not specified. Forms cytoplasmic puncta.           Cytoplasm   \n", "...                                                 ...                 ...   \n", "1172                          Balbiani body in oocytes.           Cytoplasm   \n", "1173  Localizes to nucleoli and stress granules (SGs...  Nucleus; Cytoplasm   \n", "1174  Localizes to nucleoli (DENV) and centrosomes (...  Nucleus; Cytoplasm   \n", "1175      Co-localizes with stress granule marker G3BP.           Cytoplasm   \n", "1176              Forms viral replication compartments.                 NaN   \n", "\n", "                           observed_ps_behavior_summary  \\\n", "0     At high expression levels, Dvl-2 forms non-mem...   \n", "1     Dvl-2 forms cytoplasmic puncta exclusively und...   \n", "2     Dvl2 forms cytoplasmic puncta when overexpress...   \n", "3     GFP-DIX forms puncta identical to full-length ...   \n", "4     Axin forms puncta morphologically similar to D...   \n", "...                                                 ...   \n", "1172  Self-assembles into a solid amyloid aggregate ...   \n", "1173  Forms dynamic condensates via LLPS; localizes ...   \n", "1174  Implicated in MLO localization; modulates repl...   \n", "1175  Undergoes LLPS with viral RNA; recruits replic...   \n", "1176  Forms phase-separated compartments with RNA; p...   \n", "\n", "                         key_protein_regions_studied_ps  \\\n", "0     Full-length Dvl-2 and mutants: ΔDIX (deletion ...   \n", "1     Full-length HA-tagged Dvl-2 (wild-type). DIX d...   \n", "2     Full-length Dvl2. DIX domain (aa 1-114) necess...   \n", "3                       Isolated DIX domain (aa 1-114).   \n", "4     Full-length Axin (C-terminal GFP tag essential...   \n", "...                                                 ...   \n", "1172  Self-assembles into amyloid-like structures fo...   \n", "1173  N-terminal intrinsically disordered region (ID...   \n", "1174  IDR-rich regions; phosphorylation sites in dis...   \n", "1175  Serine/arginine-rich (SR) region; phosphorylat...   \n", "1176  Intrinsically disordered regions (IDRs) in bot...   \n", "\n", "                          experiment_v2_types  \\\n", "0            in vivo droplet formation; Other   \n", "1            in vivo droplet formation; Other   \n", "2     in vivo droplet formation; in vivo FRAP   \n", "3                                in vivo FRAP   \n", "4                                in vivo FRAP   \n", "...                                       ...   \n", "1172                                      NaN   \n", "1173                                      NaN   \n", "1174                                      NaN   \n", "1175                                      NaN   \n", "1176                                      NaN   \n", "\n", "     detailed_experiments_list[0].experiment_type_original  \\\n", "0      Immunofluorescence Microscopy (Puncta Formation)      \n", "1     Immunolocalization/Confocal Imaging (Transient...      \n", "2     Immunofluorescence colocalization with vesicle...      \n", "3                                Photobleaching (FRAP).      \n", "4                                Photobleaching (FRAP).      \n", "...                                                 ...      \n", "1172                                                NaN      \n", "1173                                                NaN      \n", "1174                                                NaN      \n", "1175                                                NaN      \n", "1176                                                NaN      \n", "\n", "           detailed_experiments_list[0].constructs_used  ...  \\\n", "0                Full-length HA-Dvl-2-E<PERSON><PERSON>, HA-Dvl-2-ER  ...   \n", "1     HA-Dvl-2-E<PERSON><PERSON> (full-length), HA-Dvl-2-ER (estr...  ...   \n", "2                      HA-Dvl2, GFP-Dvl2 (full-length).  ...   \n", "3                            GFP-DIX (isolated domain).  ...   \n", "4                               GFP-Axin (full-length).  ...   \n", "...                                                 ...  ...   \n", "1172                                                NaN  ...   \n", "1173                                                NaN  ...   \n", "1174                                                NaN  ...   \n", "1175                                                NaN  ...   \n", "1176                                                NaN  ...   \n", "\n", "     entities[1].co_ps_partner_type entities[2].gene_name  \\\n", "0                               NaN                   NaN   \n", "1                               NaN                   NaN   \n", "2                               NaN                   NaN   \n", "3                               NaN                   NaN   \n", "4                               NaN                   NaN   \n", "...                             ...                   ...   \n", "1172                            NaN                   NaN   \n", "1173                            NaN                   NaN   \n", "1174                            NaN                   NaN   \n", "1175                            NaN                   NaN   \n", "1176                            NaN                   NaN   \n", "\n", "     entities[2].organism entities[2].mlo_location  \\\n", "0                     NaN                      NaN   \n", "1                     NaN                      NaN   \n", "2                     NaN                      NaN   \n", "3                     NaN                      NaN   \n", "4                     NaN                      NaN   \n", "...                   ...                      ...   \n", "1172                  NaN                      NaN   \n", "1173                  NaN                      NaN   \n", "1174                  NaN                      NaN   \n", "1175                  NaN                      NaN   \n", "1176                  NaN                      NaN   \n", "\n", "     entities[2].ps_verification_experiment_type  \\\n", "0                                            NaN   \n", "1                                            NaN   \n", "2                                            NaN   \n", "3                                            NaN   \n", "4                                            NaN   \n", "...                                          ...   \n", "1172                                         NaN   \n", "1173                                         NaN   \n", "1174                                         NaN   \n", "1175                                         NaN   \n", "1176                                         NaN   \n", "\n", "     entities[2].experimental_conditions_summary  \\\n", "0                                            NaN   \n", "1                                            NaN   \n", "2                                            NaN   \n", "3                                            NaN   \n", "4                                            NaN   \n", "...                                          ...   \n", "1172                                         NaN   \n", "1173                                         NaN   \n", "1174                                         NaN   \n", "1175                                         NaN   \n", "1176                                         NaN   \n", "\n", "     entities[2].protein_region_involved_ps entities[2].droplet_state  \\\n", "0                                       NaN                       NaN   \n", "1                                       NaN                       NaN   \n", "2                                       NaN                       NaN   \n", "3                                       NaN                       NaN   \n", "4                                       NaN                       NaN   \n", "...                                     ...                       ...   \n", "1172                                    NaN                       NaN   \n", "1173                                    NaN                       NaN   \n", "1174                                    NaN                       NaN   \n", "1175                                    NaN                       NaN   \n", "1176                                    NaN                       NaN   \n", "\n", "     entities[2].droplet_state_transition_observed  \\\n", "0                                              NaN   \n", "1                                              NaN   \n", "2                                              NaN   \n", "3                                              NaN   \n", "4                                              NaN   \n", "...                                            ...   \n", "1172                                           NaN   \n", "1173                                           NaN   \n", "1174                                           NaN   \n", "1175                                           NaN   \n", "1176                                           NaN   \n", "\n", "     entities[2].co_ps_partner_type  \n", "0                               NaN  \n", "1                               NaN  \n", "2                               NaN  \n", "3                               NaN  \n", "4                               NaN  \n", "...                             ...  \n", "1172                            NaN  \n", "1173                            NaN  \n", "1174                            NaN  \n", "1175                            NaN  \n", "1176                            NaN  \n", "\n", "[1177 rows x 122 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["aio0603 = pd.read_csv('/home/<USER>/PhasesepDBV3/test0603/gene_master_table.csv')\n", "aio0603 = aio0603.rename(columns={'article_id':'PMID'})\n", "aio0603"]}, {"cell_type": "code", "execution_count": 12, "id": "f2a38783", "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["Index(['PMID', 'gene_name', 'organism', 'mlo_association_summary', 'location',\n", "       'observed_ps_behavior_summary', 'key_protein_regions_studied_ps',\n", "       'experiment_v2_types',\n", "       'detailed_experiments_list[0].experiment_type_original',\n", "       'detailed_experiments_list[0].constructs_used',\n", "       ...\n", "       'entities[1].co_ps_partner_type', 'entities[2].gene_name',\n", "       'entities[2].organism', 'entities[2].mlo_location',\n", "       'entities[2].ps_verification_experiment_type',\n", "       'entities[2].experimental_conditions_summary',\n", "       'entities[2].protein_region_involved_ps', 'entities[2].droplet_state',\n", "       'entities[2].droplet_state_transition_observed',\n", "       'entities[2].co_ps_partner_type'],\n", "      dtype='object', length=122)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["aio0603.columns"]}, {"cell_type": "code", "execution_count": 16, "id": "96125b20", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PMID</th>\n", "      <th>gene_name</th>\n", "      <th>organism</th>\n", "      <th>location</th>\n", "      <th>material_state</th>\n", "      <th>experiment</th>\n", "      <th>MLO</th>\n", "      <th>class</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>16263761</td>\n", "      <td>Dvl-2</td>\n", "      <td>Homo sapiens</td>\n", "      <td>Cytoplasm</td>\n", "      <td>Not specified</td>\n", "      <td>in vivo droplet formation; Other</td>\n", "      <td>Not specified. Cytoplasmic puncta did not colo...</td>\n", "      <td>PS-other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>16263761</td>\n", "      <td>Dvl-2, <PERSON><PERSON><PERSON><PERSON>-2</td>\n", "      <td>*Homo sapiens* (primary focus). Experiments co...</td>\n", "      <td>NaN</td>\n", "      <td>solid</td>\n", "      <td>in vivo droplet formation; Other</td>\n", "      <td>Not described in this article. The cytoplasmic...</td>\n", "      <td>PS-other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>16263762</td>\n", "      <td>Dvl2, Dishevelled 2 (HA-tagged or GFP-tagged c...</td>\n", "      <td>Homo sapiens</td>\n", "      <td>Cytoplasm</td>\n", "      <td>solid</td>\n", "      <td>in vivo droplet formation; in vivo FRAP</td>\n", "      <td>No specific membraneless organelle (MLO) assoc...</td>\n", "      <td>PS-other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>16263762</td>\n", "      <td>GFP-DIX (DIX domain of Dvl2, aa 1-114, C-termi...</td>\n", "      <td>Homo sapiens</td>\n", "      <td>Cytoplasm</td>\n", "      <td>solid</td>\n", "      <td>in vivo FRAP</td>\n", "      <td>Not specified. Forms cytoplasmic puncta.</td>\n", "      <td>PS-other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>16263762</td>\n", "      <td>GFP-Axin (human Axin isoform a, C-terminally t...</td>\n", "      <td>Homo sapiens</td>\n", "      <td>Cytoplasm</td>\n", "      <td>solid</td>\n", "      <td>in vivo FRAP</td>\n", "      <td>Not specified. Forms cytoplasmic puncta.</td>\n", "      <td>PS-other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1172</th>\n", "      <td>34450138</td>\n", "      <td>Xvelo</td>\n", "      <td><PERSON><PERSON><PERSON> laevis</td>\n", "      <td>Cytoplasm</td>\n", "      <td>solid</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON><PERSON>i body in oocytes.</td>\n", "      <td>PS-other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1173</th>\n", "      <td>34452345</td>\n", "      <td>Core protein</td>\n", "      <td>Japanese encephalitis virus (JEV), Dengue viru...</td>\n", "      <td>Nucleus; Cytoplasm</td>\n", "      <td>liquid</td>\n", "      <td>NaN</td>\n", "      <td>Localizes to nucleoli and stress granules (SGs...</td>\n", "      <td>PS-other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1174</th>\n", "      <td>34452345</td>\n", "      <td>NS5</td>\n", "      <td>Dengue virus (DENV), Zika virus (ZIKV)</td>\n", "      <td>Nucleus; Cytoplasm</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Localizes to nucleoli (DENV) and centrosomes (...</td>\n", "      <td>PS-other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1175</th>\n", "      <td>34452345</td>\n", "      <td>Nucleoprotein</td>\n", "      <td>Severe acute respiratory syndrome coronavirus ...</td>\n", "      <td>Cytoplasm</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Co-localizes with stress granule marker G3BP.</td>\n", "      <td>PS-other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1176</th>\n", "      <td>34452345</td>\n", "      <td>Nucleoprotein and Phosphoprotein</td>\n", "      <td>Measles virus (MeV)</td>\n", "      <td>NaN</td>\n", "      <td>liquid</td>\n", "      <td>NaN</td>\n", "      <td>Forms viral replication compartments.</td>\n", "      <td>PS-other</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1177 rows × 8 columns</p>\n", "</div>"], "text/plain": ["          PMID                                          gene_name  \\\n", "0     16263761                                              Dvl-2   \n", "1     16263761                               Dvl-2, <PERSON><PERSON><PERSON><PERSON>-2   \n", "2     16263762  Dvl2, Dishevelled 2 (HA-tagged or GFP-tagged c...   \n", "3     16263762  GFP-DIX (DIX domain of Dvl2, aa 1-114, C-termi...   \n", "4     16263762  GFP-Axin (human Axin isoform a, C-terminally t...   \n", "...        ...                                                ...   \n", "1172  34450138                                              Xvelo   \n", "1173  34452345                                       Core protein   \n", "1174  34452345                                                NS5   \n", "1175  34452345                                      Nucleoprotein   \n", "1176  34452345                   Nucleoprotein and Phosphoprotein   \n", "\n", "                                               organism            location  \\\n", "0                                          Homo sapiens           Cytoplasm   \n", "1     *Homo sapiens* (primary focus). Experiments co...                 NaN   \n", "2                                          Homo sapiens           Cytoplasm   \n", "3                                          Homo sapiens           Cytoplasm   \n", "4                                          Homo sapiens           Cytoplasm   \n", "...                                                 ...                 ...   \n", "1172                                     Xenopus laevis           Cytoplasm   \n", "1173  Japanese encephalitis virus (JEV), Dengue viru...  Nucleus; Cytoplasm   \n", "1174             Dengue virus (DENV), Zika virus (ZIKV)  Nucleus; Cytoplasm   \n", "1175  Severe acute respiratory syndrome coronavirus ...           Cytoplasm   \n", "1176                                Measles virus (MeV)                 NaN   \n", "\n", "     material_state                               experiment  \\\n", "0     Not specified         in vivo droplet formation; Other   \n", "1             solid         in vivo droplet formation; Other   \n", "2             solid  in vivo droplet formation; in vivo FRAP   \n", "3             solid                             in vivo FRAP   \n", "4             solid                             in vivo FRAP   \n", "...             ...                                      ...   \n", "1172          solid                                      NaN   \n", "1173         liquid                                      NaN   \n", "1174            NaN                                      NaN   \n", "1175            NaN                                      NaN   \n", "1176         liquid                                      NaN   \n", "\n", "                                                    MLO     class  \n", "0     Not specified. Cytoplasmic puncta did not colo...  PS-other  \n", "1     Not described in this article. The cytoplasmic...  PS-other  \n", "2     No specific membraneless organelle (MLO) assoc...  PS-other  \n", "3              Not specified. Forms cytoplasmic puncta.  PS-other  \n", "4              Not specified. Forms cytoplasmic puncta.  PS-other  \n", "...                                                 ...       ...  \n", "1172                          Balbiani body in oocytes.  PS-other  \n", "1173  Localizes to nucleoli and stress granules (SGs...  PS-other  \n", "1174  Localizes to nucleoli (DENV) and centrosomes (...  PS-other  \n", "1175      Co-localizes with stress granule marker G3BP.  PS-other  \n", "1176              Forms viral replication compartments.  PS-other  \n", "\n", "[1177 rows x 8 columns]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["aio0603_1 = aio0603[['PMID','gene_name','organism','location','material_state_v2','experiment_v2_types','mlo_association_summary','class']]\n", "aio0603_1 = aio0603_1.rename(columns = {'material_state_v2':'material_state', 'experiment_v2_types':'experiment', 'mlo_association_summary':'MLO'})\n", "aio0603_1"]}, {"cell_type": "code", "execution_count": 29, "id": "6e88f504", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PMID</th>\n", "      <th>gene_name</th>\n", "      <th>organism</th>\n", "      <th>location</th>\n", "      <th>material_state</th>\n", "      <th>experiment</th>\n", "      <th>MLO</th>\n", "      <th>class</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1015</th>\n", "      <td>33479219</td>\n", "      <td>SARS-CoV-2 nucleocapsid protein</td>\n", "      <td>SARS-CoV-2</td>\n", "      <td>Cytoplasm</td>\n", "      <td>liquid; hydrogel; solid</td>\n", "      <td>in vivo droplet formation; in vivo FRAP; Other...</td>\n", "      <td>Localizes to and forms part of stress granules...</td>\n", "      <td>PS-other</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          PMID                        gene_name    organism   location  \\\n", "1015  33479219  SARS-CoV-2 nucleocapsid protein  SARS-CoV-2  Cytoplasm   \n", "\n", "               material_state  \\\n", "1015  liquid; hydrogel; solid   \n", "\n", "                                             experiment  \\\n", "1015  in vivo droplet formation; in vivo FRAP; Other...   \n", "\n", "                                                    MLO     class  \n", "1015  Localizes to and forms part of stress granules...  PS-other  "]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["aio0603_1[aio0603_1['PMID'] == 33479219]"]}, {"cell_type": "code", "execution_count": 33, "id": "42a60387", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PMID</th>\n", "      <th>organism</th>\n", "      <th>gene_name</th>\n", "      <th>location</th>\n", "      <th>MLO</th>\n", "      <th>experiment</th>\n", "      <th>class</th>\n", "      <th>material_state</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>33479219</td>\n", "      <td>Severe acute respiratory syndrome coronavirus 2</td>\n", "      <td>N</td>\n", "      <td>Cytoplasm</td>\n", "      <td>SARS-CoV-2 N condensate</td>\n", "      <td>in vitro droplet formation,in vivo droplet for...</td>\n", "      <td>PS-self</td>\n", "      <td>liquid,hydrogel,solid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>33479198</td>\n", "      <td>Severe acute respiratory syndrome coronavirus 2</td>\n", "      <td>N</td>\n", "      <td>Cytoplasm</td>\n", "      <td>SARS-CoV-2 N condensate</td>\n", "      <td>in vitro droplet formation,in vitro FRAP,in vi...</td>\n", "      <td>PS-self</td>\n", "      <td>liquid,hydrogel</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>27212236</td>\n", "      <td>Homo sapiens</td>\n", "      <td>NPM1</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Nucleolus</td>\n", "      <td>in vitro droplet formation,in vitro FRAP,in vi...</td>\n", "      <td>PS-other</td>\n", "      <td>liquid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>32487073</td>\n", "      <td>Homo sapiens</td>\n", "      <td>GR</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>_</td>\n", "      <td>in vivo droplet formation,in vivo FRAP</td>\n", "      <td>PS-other</td>\n", "      <td>liquid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>28680096</td>\n", "      <td>Rabies virus</td>\n", "      <td>P</td>\n", "      <td>Cytoplasm</td>\n", "      <td>Negri body</td>\n", "      <td>in vivo droplet formation,in vivo FRAP</td>\n", "      <td>PS-other</td>\n", "      <td>liquid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1211</th>\n", "      <td>32878896</td>\n", "      <td>Human respiratory syncytial virus A</td>\n", "      <td>P</td>\n", "      <td>_</td>\n", "      <td>Inclusion body</td>\n", "      <td>in vivo droplet formation,in vivo FRAP</td>\n", "      <td>PS-other</td>\n", "      <td>liquid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1212</th>\n", "      <td>30826453</td>\n", "      <td>Vesicular stomatitis Indiana virus</td>\n", "      <td>P</td>\n", "      <td>Cytoplasm</td>\n", "      <td>_</td>\n", "      <td>in vivo droplet formation,in vivo FRAP</td>\n", "      <td>PS-other</td>\n", "      <td>liquid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1213</th>\n", "      <td>30181255</td>\n", "      <td>Vesicular stomatitis Indiana virus</td>\n", "      <td>P</td>\n", "      <td>Cytoplasm</td>\n", "      <td>_</td>\n", "      <td>in vivo droplet formation,in vivo FRAP</td>\n", "      <td>PS-other</td>\n", "      <td>liquid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1214</th>\n", "      <td>32169085</td>\n", "      <td>Saccharomyces cerevisiae</td>\n", "      <td>Osh3</td>\n", "      <td>Cytoplasm</td>\n", "      <td>Stress granule</td>\n", "      <td>in vitro droplet formation,in vivo droplet for...</td>\n", "      <td>PS-self</td>\n", "      <td>hydrogel,solid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1215</th>\n", "      <td>31267591</td>\n", "      <td>Homo sapiens</td>\n", "      <td>53BP1</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>DNA damage foci</td>\n", "      <td>in vitro droplet formation,in vivo droplet for...</td>\n", "      <td>PS-self</td>\n", "      <td>liquid</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>951 rows × 8 columns</p>\n", "</div>"], "text/plain": ["          PMID                                         organism gene_name  \\\n", "0     33479219  Severe acute respiratory syndrome coronavirus 2         N   \n", "1     33479198  Severe acute respiratory syndrome coronavirus 2         N   \n", "2     27212236                                     Homo sapiens      NPM1   \n", "4     32487073                                     Homo sapiens        GR   \n", "6     28680096                                     Rabies virus         P   \n", "...        ...                                              ...       ...   \n", "1211  32878896              Human respiratory syncytial virus A         P   \n", "1212  30826453               Vesicular stomatitis Indiana virus         P   \n", "1213  30181255               Vesicular stomatitis Indiana virus         P   \n", "1214  32169085                         Saccharomyces cerevisiae      Osh3   \n", "1215  31267591                                     Homo sapiens     53BP1   \n", "\n", "       location                      MLO  \\\n", "0     Cytoplasm  SARS-CoV-2 N condensate   \n", "1     Cytoplasm  SARS-CoV-2 N condensate   \n", "2       Nucleus                Nucleolus   \n", "4       Nucleus                        _   \n", "6     Cytoplasm               Negri body   \n", "...         ...                      ...   \n", "1211          _           Inclusion body   \n", "1212  Cytoplasm                        _   \n", "1213  Cytoplasm                        _   \n", "1214  Cytoplasm           Stress granule   \n", "1215    Nucleus          DNA damage foci   \n", "\n", "                                             experiment     class  \\\n", "0     in vitro droplet formation,in vivo droplet for...   PS-self   \n", "1     in vitro droplet formation,in vitro FRAP,in vi...   PS-self   \n", "2     in vitro droplet formation,in vitro FRAP,in vi...  PS-other   \n", "4                in vivo droplet formation,in vivo FRAP  PS-other   \n", "6                in vivo droplet formation,in vivo FRAP  PS-other   \n", "...                                                 ...       ...   \n", "1211             in vivo droplet formation,in vivo FRAP  PS-other   \n", "1212             in vivo droplet formation,in vivo FRAP  PS-other   \n", "1213             in vivo droplet formation,in vivo FRAP  PS-other   \n", "1214  in vitro droplet formation,in vivo droplet for...   PS-self   \n", "1215  in vitro droplet formation,in vivo droplet for...   PS-self   \n", "\n", "             material_state  \n", "0     liquid,hydrogel,solid  \n", "1           liquid,hydrogel  \n", "2                    liquid  \n", "4                    liquid  \n", "6                    liquid  \n", "...                     ...  \n", "1211                 liquid  \n", "1212                 liquid  \n", "1213                 liquid  \n", "1214         hydrogel,solid  \n", "1215                 liquid  \n", "\n", "[951 rows x 8 columns]"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["#只统计在这次文献中的列表\n", "lis2 = processed_lis_1[processed_lis_1.PMID.isin(list(set(aio0603_1.PMID)))]\n", "lis2"]}, {"cell_type": "code", "execution_count": 36, "id": "f791ea8a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PMID</th>\n", "      <th>organism</th>\n", "      <th>gene_name</th>\n", "      <th>location</th>\n", "      <th>MLO</th>\n", "      <th>experiment</th>\n", "      <th>class</th>\n", "      <th>material_state</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>33479219</td>\n", "      <td>Severe acute respiratory syndrome coronavirus 2</td>\n", "      <td>N</td>\n", "      <td>Cytoplasm</td>\n", "      <td>SARS-CoV-2 N condensate</td>\n", "      <td>in vitro droplet formation,in vivo droplet for...</td>\n", "      <td>PS-self</td>\n", "      <td>liquid,hydrogel,solid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>33479198</td>\n", "      <td>Severe acute respiratory syndrome coronavirus 2</td>\n", "      <td>N</td>\n", "      <td>Cytoplasm</td>\n", "      <td>SARS-CoV-2 N condensate</td>\n", "      <td>in vitro droplet formation,in vitro FRAP,in vi...</td>\n", "      <td>PS-self</td>\n", "      <td>liquid,hydrogel</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>27212236</td>\n", "      <td>Homo sapiens</td>\n", "      <td>NPM1</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Nucleolus</td>\n", "      <td>in vitro droplet formation,in vitro FRAP,in vi...</td>\n", "      <td>PS-other</td>\n", "      <td>liquid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>32487073</td>\n", "      <td>Homo sapiens</td>\n", "      <td>GR</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>_</td>\n", "      <td>in vivo droplet formation,in vivo FRAP</td>\n", "      <td>PS-other</td>\n", "      <td>liquid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>28680096</td>\n", "      <td>Rabies virus</td>\n", "      <td>P</td>\n", "      <td>Cytoplasm</td>\n", "      <td>Negri body</td>\n", "      <td>in vivo droplet formation,in vivo FRAP</td>\n", "      <td>PS-other</td>\n", "      <td>liquid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1211</th>\n", "      <td>32878896</td>\n", "      <td>Human respiratory syncytial virus A</td>\n", "      <td>P</td>\n", "      <td>_</td>\n", "      <td>Inclusion body</td>\n", "      <td>in vivo droplet formation,in vivo FRAP</td>\n", "      <td>PS-other</td>\n", "      <td>liquid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1212</th>\n", "      <td>30826453</td>\n", "      <td>Vesicular stomatitis Indiana virus</td>\n", "      <td>P</td>\n", "      <td>Cytoplasm</td>\n", "      <td>_</td>\n", "      <td>in vivo droplet formation,in vivo FRAP</td>\n", "      <td>PS-other</td>\n", "      <td>liquid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1213</th>\n", "      <td>30181255</td>\n", "      <td>Vesicular stomatitis Indiana virus</td>\n", "      <td>P</td>\n", "      <td>Cytoplasm</td>\n", "      <td>_</td>\n", "      <td>in vivo droplet formation,in vivo FRAP</td>\n", "      <td>PS-other</td>\n", "      <td>liquid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1214</th>\n", "      <td>32169085</td>\n", "      <td>Saccharomyces cerevisiae</td>\n", "      <td>Osh3</td>\n", "      <td>Cytoplasm</td>\n", "      <td>Stress granule</td>\n", "      <td>in vitro droplet formation,in vivo droplet for...</td>\n", "      <td>PS-self</td>\n", "      <td>hydrogel,solid</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1215</th>\n", "      <td>31267591</td>\n", "      <td>Homo sapiens</td>\n", "      <td>53BP1</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>DNA damage foci</td>\n", "      <td>in vitro droplet formation,in vivo droplet for...</td>\n", "      <td>PS-self</td>\n", "      <td>liquid</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>951 rows × 8 columns</p>\n", "</div>"], "text/plain": ["          PMID                                         organism gene_name  \\\n", "0     33479219  Severe acute respiratory syndrome coronavirus 2         N   \n", "1     33479198  Severe acute respiratory syndrome coronavirus 2         N   \n", "2     27212236                                     Homo sapiens      NPM1   \n", "4     32487073                                     Homo sapiens        GR   \n", "6     28680096                                     Rabies virus         P   \n", "...        ...                                              ...       ...   \n", "1211  32878896              Human respiratory syncytial virus A         P   \n", "1212  30826453               Vesicular stomatitis Indiana virus         P   \n", "1213  30181255               Vesicular stomatitis Indiana virus         P   \n", "1214  32169085                         Saccharomyces cerevisiae      Osh3   \n", "1215  31267591                                     Homo sapiens     53BP1   \n", "\n", "       location                      MLO  \\\n", "0     Cytoplasm  SARS-CoV-2 N condensate   \n", "1     Cytoplasm  SARS-CoV-2 N condensate   \n", "2       Nucleus                Nucleolus   \n", "4       Nucleus                        _   \n", "6     Cytoplasm               Negri body   \n", "...         ...                      ...   \n", "1211          _           Inclusion body   \n", "1212  Cytoplasm                        _   \n", "1213  Cytoplasm                        _   \n", "1214  Cytoplasm           Stress granule   \n", "1215    Nucleus          DNA damage foci   \n", "\n", "                                             experiment     class  \\\n", "0     in vitro droplet formation,in vivo droplet for...   PS-self   \n", "1     in vitro droplet formation,in vitro FRAP,in vi...   PS-self   \n", "2     in vitro droplet formation,in vitro FRAP,in vi...  PS-other   \n", "4                in vivo droplet formation,in vivo FRAP  PS-other   \n", "6                in vivo droplet formation,in vivo FRAP  PS-other   \n", "...                                                 ...       ...   \n", "1211             in vivo droplet formation,in vivo FRAP  PS-other   \n", "1212             in vivo droplet formation,in vivo FRAP  PS-other   \n", "1213             in vivo droplet formation,in vivo FRAP  PS-other   \n", "1214  in vitro droplet formation,in vivo droplet for...   PS-self   \n", "1215  in vitro droplet formation,in vivo droplet for...   PS-self   \n", "\n", "             material_state  \n", "0     liquid,hydrogel,solid  \n", "1           liquid,hydrogel  \n", "2                    liquid  \n", "4                    liquid  \n", "6                    liquid  \n", "...                     ...  \n", "1211                 liquid  \n", "1212                 liquid  \n", "1213                 liquid  \n", "1214         hydrogel,solid  \n", "1215                 liquid  \n", "\n", "[951 rows x 8 columns]"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["lis2"]}, {"cell_type": "code", "execution_count": 39, "id": "64eaea10", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== 总体准确率 =====\n", "基因发现率: 62.88%\n", "所有字段完全正确率: 1.89%\n", "\n", "各字段准确率:\n", "organism       : 39.12%\n", "location       : 33.86%\n", "material_state : 31.55%\n", "experiment     : 10.73%\n", "class          : 49.84%\n", "\n", "错误率最高的10个基因:\n", "          total_records  error_rate  all_correct_rate\n", "ref_gene                                             \n", "dbp1                  1         1.0               0.0\n", "als5p                 1         1.0               0.0\n", "rbm20                 1         1.0               0.0\n", "ubqln4                1         1.0               0.0\n", "ap7                   1         1.0               0.0\n", "rbm3                  1         1.0               0.0\n", "ch1                   1         0.8               0.0\n", "survivin              2         0.8               0.0\n", "stt2                  1         0.8               0.0\n", "hspa1a                1         0.8               0.0\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from collections import defaultdict\n", "\n", "# 1. 数据准备和预处理\n", "def preprocess_data(lis2, aio0603_1):\n", "    \"\"\"数据预处理：清洗、标准化和类型转换\"\"\"\n", "    # 处理lis2中的PMID（针对逗号分隔的情况）\n", "    if isinstance(lis2['PMID'].iloc[0], str):\n", "        # 拆分逗号分隔的PMID\n", "        lis2_expanded = lis2.copy()\n", "        lis2_expanded['PMID'] = lis2_expanded['PMID'].str.split(',')\n", "        lis2_expanded = lis2_expanded.explode('PMID')\n", "        lis2_expanded['PMID'] = pd.to_numeric(lis2_expanded['PMID'], errors='coerce')\n", "        lis2_expanded = lis2_expanded.dropna(subset=['PMID'])\n", "        lis2_expanded['PMID'] = lis2_expanded['PMID'].astype(int)\n", "    else:\n", "        lis2_expanded = lis2.copy()\n", "    \n", "    # 标准化gene_name（小写字母和去除空格）\n", "    for df in [lis2_expanded, aio0603_1]:\n", "        # 确保gene_name是字符串类型\n", "        df['gene_name'] = df['gene_name'].astype(str).str.lower().str.strip()\n", "        # 处理可能的NaN值\n", "        df['gene_name'] = df['gene_name'].replace('nan', '')\n", "    \n", "    return lis2_expanded, aio0603_1\n", "\n", "# 2. 比较函数 - 统一版本（解决分隔符差异）\n", "def compare_fields(ref_value, ai_value, ref_separator=',', ai_separator=';'):\n", "    \"\"\"比较两个字段值（支持不同分隔符）\"\"\"\n", "    # 处理NaN值\n", "    if pd.isna(ref_value) or ref_value == '':\n", "        ref_value = np.nan\n", "    if pd.isna(ai_value) or ai_value == '':\n", "        ai_value = np.nan\n", "    \n", "    # 如果两者都是NaN，视为匹配\n", "    if pd.isna(ref_value) and pd.isna(ai_value):\n", "        return True\n", "    # 如果只有一个为NaN，视为不匹配\n", "    if pd.isna(ref_value) or pd.isna(ai_value):\n", "        return False\n", "    \n", "    # 处理参考值(lis2) - 使用逗号分隔\n", "    ref_val = str(ref_value).lower()\n", "    if ref_separator in ref_val:\n", "        ref_set = set(x.strip() for x in ref_val.split(ref_separator))\n", "    else:\n", "        ref_set = {ref_val.strip()}\n", "    \n", "    # 处理AI值(aio0603_1) - 使用分号分隔\n", "    ai_val = str(ai_value).lower()\n", "    if ai_separator in ai_val:\n", "        ai_set = set(x.strip() for x in ai_val.split(ai_separator))\n", "    else:\n", "        ai_set = {ai_val.strip()}\n", "    \n", "    return ref_set == ai_set\n", "\n", "# 3. 主分析函数\n", "def analyze_accuracy(lis2, aio0603_1, columns_to_compare):\n", "    \"\"\"执行准确率分析\"\"\"\n", "    # 预处理数据\n", "    lis2, aio0603_1 = preprocess_data(lis2, aio0603_1)\n", "    \n", "    # 结果存储\n", "    results = defaultdict(list)\n", "    column_scores = defaultdict(list)\n", "    \n", "    # 按PMID分组处理\n", "    for pmid in sorted(lis2['PMID'].unique()):\n", "        ref_rows = lis2[lis2['PMID'] == pmid]\n", "        ai_rows = aio0603_1[aio0603_1['PMID'] == pmid]\n", "        \n", "        # 跳过没有AI记录的PMID\n", "        if ai_rows.empty:\n", "            continue\n", "            \n", "        # 分析每条参考基因记录\n", "        for _, ref_row in ref_rows.iterrows():\n", "            ref_gene = ref_row['gene_name']\n", "            found = False\n", "            \n", "            # 检查AI数据中是否存在匹配的基因（包含匹配）\n", "            for _, ai_row in ai_rows.iterrows():\n", "                ai_gene = ai_row['gene_name']\n", "                \n", "                # 确保ai_gene是字符串类型\n", "                ai_gene_str = str(ai_gene).lower()\n", "                \n", "                # 检查基因名称是否匹配（包含关系）\n", "                if ref_gene in ai_gene_str:\n", "                    found = True\n", "                    # 比较所有指定字段\n", "                    all_correct = True\n", "                    row_result = {'PMID': pmid, 'ref_gene': ref_gene, 'ai_gene': ai_gene}\n", "                    \n", "                    # 逐一比较每个字段（使用统一分隔符规则）\n", "                    for col in columns_to_compare:\n", "                        # lis2使用逗号，aio0603_1使用分号\n", "                        col_match = compare_fields(\n", "                            ref_row.get(col, pd.NA),\n", "                            ai_row.get(col, pd.NA),\n", "                            ref_separator=',',  # lis2的所有字段都用逗号\n", "                            ai_separator=';'    # aio0603_1的所有字段都用分号\n", "                        )\n", "                        row_result[f'{col}_correct'] = col_match\n", "                        column_scores[col].append(col_match)\n", "                        if not col_match:\n", "                            all_correct = False\n", "                    \n", "                    # 记录所有字段是否全部正确\n", "                    row_result['all_correct'] = all_correct\n", "                    column_scores['all'].append(all_correct)\n", "                    results[pmid].append(row_result)\n", "                    break\n", "                    \n", "            # 如果没有找到匹配的AI基因记录\n", "            if not found:\n", "                row_result = {'PMID': pmid, 'ref_gene': ref_gene, 'ai_gene': 'NOT_FOUND'}\n", "                for col in columns_to_compare:\n", "                    row_result[f'{col}_correct'] = False\n", "                row_result['all_correct'] = False\n", "                results[pmid].append(row_result)\n", "                column_scores['all'].append(False)\n", "    \n", "    return results, column_scores\n", "\n", "# 4. 结果汇总函数\n", "def summarize_results(results, column_scores):\n", "    \"\"\"汇总准确率结果\"\"\"\n", "    # 将结果转换为DataFrame\n", "    all_results = []\n", "    for pmid, data in results.items():\n", "        all_results.extend(data)\n", "    results_df = pd.DataFrame(all_results)\n", "    \n", "    # 确保有记录\n", "    if results_df.empty:\n", "        print(\"警告：没有找到匹配的记录\")\n", "        return {}, pd.DataFrame(), {}\n", "    \n", "    # 计算各个字段的准确率\n", "    summary = {}\n", "    total_records = len(results_df)\n", "    \n", "    for col in ['organism', 'location', 'material_state', 'experiment', 'class']:\n", "        correct_count = results_df[f'{col}_correct'].sum()\n", "        summary[f'{col}_accuracy'] = correct_count / total_records if total_records else 0\n", "    \n", "    # 所有字段全部正确的比例\n", "    summary['all_fields_accuracy'] = results_df['all_correct'].sum() / total_records if total_records else 0\n", "    \n", "    # 基因发现率\n", "    summary['gene_discovery_rate'] = 1 - (results_df['ai_gene'] == 'NOT_FOUND').sum() / total_records\n", "    \n", "    # 各PMID的详细准确率\n", "    pmid_summary = {}\n", "    for pmid, data in results.items():\n", "        pmid_data = {\n", "            'gene_count': len(data),\n", "            'genes_found': sum(1 for r in data if r['ai_gene'] != 'NOT_FOUND'),\n", "            'all_correct_count': sum(1 for r in data if r.get('all_correct', False))\n", "        }\n", "        pmid_data['gene_found_rate'] = pmid_data['genes_found'] / pmid_data['gene_count'] if pmid_data['gene_count'] > 0 else 0\n", "        pmid_data['all_correct_rate'] = pmid_data['all_correct_count'] / pmid_data['gene_count'] if pmid_data['gene_count'] > 0 else 0\n", "        pmid_summary[pmid] = pmid_data\n", "    \n", "    return summary, results_df, pmid_summary\n", "\n", "# 5. 主执行流程\n", "def main_accuracy_analysis():\n", "    # 使用全局变量（在Jupyter中已加载）\n", "    lis2_ref = lis2  # 参考标准数据\n", "    aio0603_1_data = aio0603_1  # AI生成的数据\n", "    \n", "    # 需要比较的字段\n", "    columns_to_compare = ['organism', 'location', 'material_state', 'experiment', 'class']\n", "    \n", "    # 执行分析\n", "    results, column_scores = analyze_accuracy(\n", "        lis2_ref, \n", "        aio0603_1_data, \n", "        columns_to_compare\n", "    )\n", "    \n", "    # 汇总结果\n", "    summary, detailed_results, pmid_summary = summarize_results(results, column_scores)\n", "    \n", "    # 输出结果\n", "    if summary:  # 确保有结果\n", "        print(\"\\n===== 总体准确率 =====\")\n", "        print(f\"基因发现率: {summary['gene_discovery_rate']*100:.2f}%\")\n", "        print(f\"所有字段完全正确率: {summary['all_fields_accuracy']*100:.2f}%\\n\")\n", "        \n", "        print(\"各字段准确率:\")\n", "        for col in columns_to_compare:\n", "            print(f\"{col:15}: {summary[f'{col}_accuracy']*100:.2f}%\")\n", "    else:\n", "        print(\"没有结果可汇总\")\n", "    \n", "    # 输出详细结果到文件\n", "    detailed_results.to_csv('gene_accuracy_detailed_results.csv', index=False)\n", "    \n", "    # 创建PMID汇总表\n", "    pmid_df = pd.DataFrame.from_dict(pmid_summary, orient='index').reset_index().rename(columns={'index': 'PMID'})\n", "    pmid_df.to_csv('pmid_accuracy_summary.csv', index=False)\n", "    \n", "    return summary, detailed_results, pmid_df, columns_to_compare  # 返回columns_to_compare供后续使用\n", "\n", "# 执行分析\n", "if __name__ == \"__main__\":\n", "    summary, detailed_results, pmid_df, columns_to_compare = main_accuracy_analysis()\n", "    \n", "    # 确保有详细结果\n", "    if not detailed_results.empty:\n", "        # 额外分析：找出错误最多的基因\n", "        error_counts = detailed_results.copy()\n", "        for col in columns_to_compare:\n", "            if f'{col}_correct' in error_counts.columns:\n", "                error_counts[f'{col}_error'] = ~error_counts[f'{col}_correct']\n", "            else:\n", "                error_counts[f'{col}_error'] = False\n", "        \n", "        # 添加所有字段错误计数\n", "        error_columns = [f'{col}_error' for col in columns_to_compare]\n", "        error_counts['total_errors'] = error_counts[error_columns].sum(axis=1)\n", "        \n", "        # 找出错误最多的基因（至少被发现过的基因）\n", "        problematic_genes = (\n", "            error_counts[error_counts['ai_gene'] != 'NOT_FOUND']\n", "            .groupby('ref_gene')\n", "            .agg(\n", "                total_records=('ref_gene', 'count'),\n", "                error_rate=('total_errors', lambda x: x.sum()/(len(x)*len(columns_to_compare))),\n", "                all_correct_rate=('all_correct', 'mean')\n", "            )\n", "            .sort_values('error_rate', ascending=False)\n", "            .head(10)\n", "        )\n", "        \n", "        print(\"\\n错误率最高的10个基因:\")\n", "        print(problematic_genes)\n", "        \n", "        # 保存错误分析\n", "        error_counts.to_csv('gene_error_analysis.csv', index=False)\n", "    else:\n", "        print(\"没有详细结果可用于错误分析\")"]}, {"cell_type": "code", "execution_count": null, "id": "a1cb00cf", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [conda env:.conda-py]", "language": "python", "name": "conda-env-.conda-py-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 5}