# PhaSepDB LLM Agentic System

通过建立 LLM 多智能体实现文献检索、信息提取等功能。

## TODO

### Agents

- [x] 初步框架建立
- [x] 判断是否相关文献
- [x] 提取相关分子信息
- [ ] 根据相关分子信息进行信息提取
  - [ ] 修改提取分子的 agent, 目前提取到了很多不需要的分子，导致后续的实验信息提取幻觉严重
    - [ ] 添加一个 fact review agent，用于对提取到的分子进行 review，去除不需要的分子
  - [ ] ...

### 辅助功能

- [ ] 文献下载
- [ ] 文献格式转换（pdf -> md）
- [ ] ...

### Issue for all-in-one 20250603

1. 记录冗余（包括同一基因的同一表述被重复表示）：16263761
2. 同一蛋白的不同截断体/修饰/突变全部被标注（是否需要都标注出来）：17082456
3. 体外实验文章合成的多肽会被记录（是否需要标注）
4. organism 的输出还是需要再规范化：26359986
5. json 为空值：26836305, 29301985, 29650702
6. class mismatch 部分：21928802

['16263761','17082456','26359986','26836305','29301985','29650702','21928802']

### Issue for all-in-one 20250605

1. partner 需要有實驗證據
2.

## 辅助函数

- batch_run.py

```shell
# 使用逗号分隔的PMID列表
python src/core/batch_run.py --pmids 12345678,87654321

# 从文件加载PMID
python src/core/batch_run.py --file pmids.txt

# 设置并发数和禁用恢复模式
python src/core/batch_run.py --workers 10 --no-resume --file pmids.txt
```
