import streamlit as st
import os
import json
import glob
import tempfile
from datetime import datetime
from typing import Dict, List, Optional
import sys

# Add src to path for imports
sys.path.append("src")
sys.path.append(".")

try:
    from src.utils.mineru import convert_pdf_to_markdown
    from src.core.crews.all_in_one_curator import LLPSCurator
except ImportError:
    # Fallback imports
    try:
        from utils.mineru import convert_pdf_to_markdown
        from core.crews.all_in_one_curator import LLPSCurator
    except ImportError:
        st.error(
            "Could not import required modules. Please check your Python path and dependencies."
        )
        st.stop()

# Page configuration
st.set_page_config(
    page_title="PMID Processing and Validation", page_icon="🧬", layout="wide"
)


def check_existing_files(pmid: str) -> Dict[str, bool]:
    """Check if MD and JSON files exist for the given PMID"""
    output_dir = "output"

    # Look for any files starting with the PMID
    md_files = glob.glob(f"{output_dir}/{pmid}_*.md")
    json_files = glob.glob(f"{output_dir}/{pmid}_*.json")

    return {
        "md_exists": len(md_files) > 0,
        "json_exists": len(json_files) > 0,
        "md_files": md_files,
        "json_files": json_files,
    }


def get_latest_json_file(pmid: str) -> Optional[str]:
    """Get the most recent JSON file for the given PMID"""
    output_dir = "output"
    json_files = glob.glob(f"{output_dir}/{pmid}_*.json")

    if not json_files:
        return None

    # Sort by modification time, get the latest
    json_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    return json_files[0]


def process_pdf_to_markdown(pdf_file, pmid: str) -> bool:
    """Process uploaded PDF to markdown using Mineru API"""
    try:
        # Get Mineru API token from environment
        token = os.environ.get("MINERU_API")
        if not token:
            st.error("❌ Mineru API token not found in environment variables")
            st.info("💡 Please set MINERU_API in your .env file")
            return False

        # Validate PDF file
        if pdf_file.size > 50 * 1024 * 1024:  # 50MB limit
            st.error("❌ PDF file too large (max 50MB)")
            return False

        # Create temporary file for PDF
        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp_file:
            tmp_file.write(pdf_file.getvalue())
            tmp_pdf_path = tmp_file.name

        # Create output directory for markdown
        markdown_dir = f"data/input/markdown/{pmid}"
        os.makedirs(markdown_dir, exist_ok=True)

        # Show progress
        progress_bar = st.progress(0)
        status_text = st.empty()

        status_text.text("🔄 Uploading PDF to Mineru...")
        progress_bar.progress(25)

        # Convert PDF to markdown
        success = convert_pdf_to_markdown(
            pdf_path=tmp_pdf_path, output_path=markdown_dir, token=token, is_ocr=False
        )

        if success:
            progress_bar.progress(100)
            status_text.text("✅ PDF conversion completed!")
        else:
            status_text.text("❌ PDF conversion failed!")

        # Clean up temporary file
        os.unlink(tmp_pdf_path)

        return success

    except Exception as e:
        st.error(f"Error processing PDF: {str(e)}")
        return False


def run_crewai_processing(pmid: str) -> bool:
    """Run CrewAI processing for the given PMID"""
    try:
        # Check if markdown file exists
        markdown_path = f"data/input/markdown/{pmid}/{pmid}.md"
        if not os.path.exists(markdown_path):
            st.error(f"❌ Markdown file not found: {markdown_path}")
            return False

        # Show progress
        progress_bar = st.progress(0)
        status_text = st.empty()

        status_text.text("📖 Reading markdown content...")
        progress_bar.progress(20)

        # Read markdown content
        with open(markdown_path, "r", encoding="utf-8") as f:
            full_text = f.read()

        status_text.text("🤖 Running CrewAI processing...")
        progress_bar.progress(50)

        # Run CrewAI processing
        inputs = {"paper_full_text": full_text}
        LLPSCurator(pmid=pmid).crew().kickoff(inputs=inputs)

        progress_bar.progress(100)
        status_text.text("✅ CrewAI processing completed!")

        return True

    except Exception as e:
        st.error(f"❌ Error running CrewAI processing: {str(e)}")
        return False


def load_json_data(json_file_path: str) -> Optional[Dict]:
    """Load and parse JSON data"""
    try:
        with open(json_file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
        return data
    except Exception as e:
        st.error(f"Error loading JSON file: {str(e)}")
        return None


def display_protein_data(protein_data: Dict, protein_index: int):
    """Display protein data with validation options"""
    st.subheader(
        f"Protein {protein_index + 1}: {protein_data.get('gene_name', 'Unknown')}"
    )

    # Create columns for better layout
    col1, col2 = st.columns([3, 1])

    validation_results = {}

    with col1:
        # Gene name
        st.text_area(
            "Gene Name",
            value=protein_data.get("gene_name", ""),
            key=f"gene_name_{protein_index}",
            height=50,
        )

        # Organism
        st.text_area(
            "Organism",
            value=protein_data.get("organism", ""),
            key=f"organism_{protein_index}",
            height=50,
        )

        # MLO (Membraneless Organelles)
        mlo_text = (
            ", ".join(protein_data.get("mlo", [])) if protein_data.get("mlo") else ""
        )
        st.text_area(
            "MLO (Membraneless Organelles)",
            value=mlo_text,
            key=f"mlo_{protein_index}",
            height=50,
        )

        # MLO Association Summary
        st.text_area(
            "MLO Association Summary",
            value=protein_data.get("mlo_association_summary", ""),
            key=f"mlo_summary_{protein_index}",
            height=100,
        )

        # Location
        location_text = (
            ", ".join(protein_data.get("location", []))
            if protein_data.get("location")
            else ""
        )
        st.text_area(
            "Location", value=location_text, key=f"location_{protein_index}", height=50
        )

        # Observed PS Behavior Summary
        st.text_area(
            "Observed Phase Separation Behavior Summary",
            value=protein_data.get("observed_ps_behavior_summary", ""),
            key=f"ps_behavior_{protein_index}",
            height=150,
        )

        # Key Protein Regions
        st.text_area(
            "Key Protein Regions Studied for Phase Separation",
            value=protein_data.get("key_protein_regions_studied_ps", ""),
            key=f"regions_{protein_index}",
            height=100,
        )

        # Experiment Types
        exp_types_text = (
            ", ".join(protein_data.get("experiment_types", []))
            if protein_data.get("experiment_types")
            else ""
        )
        st.text_area(
            "Experiment Types",
            value=exp_types_text,
            key=f"exp_types_{protein_index}",
            height=50,
        )

        # Material State
        material_state_text = (
            ", ".join(protein_data.get("material_state", []))
            if protein_data.get("material_state")
            else ""
        )
        st.text_area(
            "Material State",
            value=material_state_text,
            key=f"material_state_{protein_index}",
            height=50,
        )

        # Overall Material Properties Summary
        st.text_area(
            "Overall Material Properties Summary",
            value=protein_data.get("overall_material_properties_summary", ""),
            key=f"material_props_{protein_index}",
            height=100,
        )

        # Functional Implications Summary
        st.text_area(
            "Functional Implications Summary",
            value=protein_data.get("functional_implications_summary", ""),
            key=f"functional_{protein_index}",
            height=150,
        )

        # Class
        st.text_area(
            "Class",
            value=protein_data.get("class_", ""),
            key=f"class_{protein_index}",
            height=50,
        )

    with col2:
        st.write("**Validation**")

        # Validation checkboxes for each field
        fields = [
            "gene_name",
            "organism",
            "mlo",
            "mlo_summary",
            "location",
            "ps_behavior",
            "regions",
            "exp_types",
            "material_state",
            "material_props",
            "functional",
            "class",
        ]

        for field in fields:
            validation_results[f"{field}_{protein_index}"] = st.checkbox(
                f"✓ {field.replace('_', ' ').title()}",
                key=f"valid_{field}_{protein_index}",
            )

    return validation_results


def check_environment():
    """Check if required environment variables are set"""
    required_vars = ["MINERU_API", "PROVIDER", "MODEL"]
    missing_vars = []

    for var in required_vars:
        if not os.environ.get(var):
            missing_vars.append(var)

    return missing_vars


def main():
    st.title("🧬 PMID Processing and Validation System")
    st.markdown("---")

    # Check environment variables
    missing_vars = check_environment()
    if missing_vars:
        st.sidebar.error(f"⚠️ Missing environment variables: {', '.join(missing_vars)}")
        st.sidebar.info("💡 Please check your .env file")
    else:
        st.sidebar.success("✅ Environment configured")

    # Sidebar for navigation
    st.sidebar.title("Navigation")
    page = st.sidebar.radio("Select Page", ["PMID Processing", "Data Validation"])

    if page == "PMID Processing":
        st.header("📄 PMID Processing")

        # PMID input
        pmid = st.text_input("Enter PMID:", placeholder="e.g., 29507397")

        if pmid:
            # Check existing files
            file_status = check_existing_files(pmid)

            col1, col2, col3 = st.columns(3)

            with col1:
                if file_status["md_exists"]:
                    st.success(f"✅ MD files found: {len(file_status['md_files'])}")
                else:
                    st.warning("⚠️ No MD files found")

            with col2:
                if file_status["json_exists"]:
                    st.success(f"✅ JSON files found: {len(file_status['json_files'])}")
                else:
                    st.warning("⚠️ No JSON files found")

            with col3:
                if file_status["md_exists"] and file_status["json_exists"]:
                    st.success("🎉 Ready for validation!")
                else:
                    st.info("📤 Upload PDF to process")

            # If files don't exist, show upload interface
            if not (file_status["md_exists"] and file_status["json_exists"]):
                st.markdown("---")
                st.subheader("📤 Upload PDF for Processing")

                uploaded_file = st.file_uploader(
                    "Choose a PDF file",
                    type="pdf",
                    help="Upload the PDF file for the specified PMID",
                )

                if uploaded_file is not None:
                    st.info(f"File uploaded: {uploaded_file.name}")

                    if st.button("🚀 Process PDF", type="primary"):
                        with st.spinner("Processing PDF to Markdown..."):
                            # Step 1: Convert PDF to Markdown
                            if process_pdf_to_markdown(uploaded_file, pmid):
                                st.success("✅ PDF converted to Markdown successfully!")

                                # Step 2: Run CrewAI processing
                                with st.spinner("Running CrewAI processing..."):
                                    if run_crewai_processing(pmid):
                                        st.success("✅ CrewAI processing completed!")
                                        st.balloons()
                                        st.rerun()
                                    else:
                                        st.error("❌ CrewAI processing failed")
                            else:
                                st.error("❌ PDF conversion failed")

    elif page == "Data Validation":
        st.header("✅ Data Validation")

        # PMID input for validation
        pmid = st.text_input("Enter PMID for validation:", placeholder="e.g., 29507397")

        if pmid:
            # Get the latest JSON file
            json_file = get_latest_json_file(pmid)

            if json_file:
                st.success(f"📄 Found JSON file: {os.path.basename(json_file)}")

                # Load JSON data
                data = load_json_data(json_file)

                if data and "proteins" in data:
                    st.info(f"Found {len(data['proteins'])} protein(s) in the data")

                    # Store validation results
                    all_validation_results = {}

                    # Display each protein
                    for i, protein in enumerate(data["proteins"]):
                        with st.expander(
                            f"Protein {i + 1}: {protein.get('gene_name', 'Unknown')}",
                            expanded=True,
                        ):
                            validation_results = display_protein_data(protein, i)
                            all_validation_results.update(validation_results)

                    # Submit button
                    st.markdown("---")
                    col1, col2, col3 = st.columns([1, 1, 1])

                    with col2:
                        if st.button(
                            "📝 Submit Validation",
                            type="primary",
                            use_container_width=True,
                        ):
                            # Count validated fields
                            total_fields = len(all_validation_results)
                            validated_fields = sum(all_validation_results.values())

                            st.success("✅ Validation submitted!")
                            st.info(
                                f"Validated {validated_fields}/{total_fields} fields ({validated_fields / total_fields * 100:.1f}%)"
                            )

                            # Here you could save the validation results to a file or database
                            validation_data = {
                                "pmid": pmid,
                                "json_file": json_file,
                                "timestamp": datetime.now().isoformat(),
                                "validation_results": all_validation_results,
                                "summary": {
                                    "total_fields": total_fields,
                                    "validated_fields": validated_fields,
                                    "validation_rate": validated_fields / total_fields,
                                },
                            }

                            # Save validation results
                            validation_file = f"output/validation_{pmid}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                            with open(validation_file, "w", encoding="utf-8") as f:
                                json.dump(
                                    validation_data, f, indent=2, ensure_ascii=False
                                )

                            st.success(
                                f"💾 Validation results saved to: {validation_file}"
                            )

                else:
                    st.error("❌ Invalid JSON format or no proteins found")
            else:
                st.warning(f"⚠️ No JSON files found for PMID: {pmid}")


if __name__ == "__main__":
    main()
