extract_molecule_info:
  description: >
    Extract the information from the paper about the given molecule: {molecule}.
    Extract the followings:
    1. Experiment type:
      * In vitro droplet formation : The molecule forms droplets in vitro by itself under certain conditions.
      * In vitro FRAP : The droplets formed by the molecule is tested by fluorescence recovery after photobleaching (FRAP).
      * In vovo droplet formation: The molecule exhibit droplet/condensate/puncta/speckle-like distribution in vivo.
      * In vivo FRAP: The droplet-like structure is tested by fluorescence recovery after photobleaching (FRAP).
      * In vitro co-localization: The molecule is recruited and distributed within the droplets formed by other molecules in vitro.
      * In vivo co-localization: The molecule is co-localized with other molecules that are markers of biomolecular condensates or membraneless organelles.
      * Other experiments: other experiments that are used to study the phase separation properties or mechanism of the molecule.
    2. Experiment Details: 
    * Comprehensive description of the experiments including but not limited to: the molecule construct (regions/mutation/truncations/PTMs e.g., 1-500 region, RGG domain, GFP-fused protein, Y28S mutant etc.), partners (other molecules involved), the conditions(cell lines or tissue or in vivo experiments, physiochemical conditions for in vitro experiments), the phenomena observed (droplet formation, FRAP, co-localization, etc.).
    3. Experiment Conclusion: The conclusion of the experiment.
    4. Publication Note: Extract and quote the EXACT original text passages from the paper that describe this experiment. Do not paraphrase or modify the text in any way. Include the complete relevant passages .
    Attention
    - Only includes the experiments involving the given molecule only: {molecule}. Leave out any other experiments that are not related to the given molecule.
    - Include ALL instances of experiments (e.g., three separate FRAP assays → three entries)
    - If there are no relevant experiments, return an empty list.
    Here is the paper info:
    {paper_info}
  expected_output: >
    A json object of biomolecules that undergo phase separation with the following format:
    {
      "Experiments": [
        {
          "experiment": "Experiment type",
          "detail": "Experimental Details",
          "conclusion": "The conclusion of the experiment",
          "note": "Publication Note"
          },
          // Repeat for ALL experimental instances
      ]
    }
  agent: extractor
