from crewai import Agent, Crew, Process, Task, LLM
from crewai.project import Crew<PERSON><PERSON>, agent, crew, task
import os
from dotenv import load_dotenv
from pydantic import BaseModel
from typing import List

# 加载.env文件
load_dotenv()

# 获取环境变量
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
ZHIPU_API_KEY = os.getenv("ZHIPU_API_KEY")
ZHIPU_BASE_URL = os.getenv("ZHIPU_BASE_URL")
ALI_API_KEY = os.getenv("ALI_API_KEY")
ALI_BASE_URL = os.getenv("ALI_BASE_URL")
ARK_API_KEY = os.getenv("ARK_API_KEY")
ARK_BASE_URL = os.getenv("ARK_BASE_URL")


# 测试 DeepSeekV3-0324 模型
# doubao-1-5-pro-32k-250115
llm_ark = LLM(
    model="openai/deepseek-v3-250324", api_key=ARK_API_KEY, base_url=ARK_BASE_URL
)


class Experiment(BaseModel):
    experiment: str
    detail: str
    conclusion: str
    note: str


class Entity(BaseModel):
    name: str
    abbreviation: str
    organism: str
    molecule_type: str

    class Config:
        extra = "allow"


class Experiments(BaseModel):
    experiments: List[Experiment]


class Entities(BaseModel):
    entities: List[Entity]


@CrewBase
class Curator:
    """
    Creating Curator crew
    params:
            pmid: str, paper PMID for file storage
    """

    agents_config = "config/agents.yaml"
    tasks_config = "config/tasks.yaml"

    def __init__(self, pmid: str):
        super().__init__()
        self.pmid = pmid

    @agent
    def finder(self) -> Agent:
        return Agent(
            config=self.agents_config["finder"],
            verbose=True,
            llm=llm_ark,
        )

    @task
    def finder_task(self) -> Task:
        return Task(
            config=self.tasks_config["finder_task"],
            output_file=f"output/{self.pmid}_biomolecules.json",
            output_pydantic=Entities,
        )

    @crew
    def crew(self) -> Crew:
        """Creates the Curator crew"""

        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            process=Process.sequential,
            verbose=True,
        )


@CrewBase
class Extractor:
    """
    Creating Extractor crew to extract molecule info from paper.
    """

    agents_config = "config/extract_agents.yaml"
    tasks_config = "config/extract_tasks.yaml"

    def __init__(self, pmid: str):
        super().__init__()
        self.pmid = pmid

    @agent
    def extractor(self) -> Agent:
        return Agent(
            config=self.agents_config["extractor"],
            verbose=True,
            llm=llm_ark,
        )

    @task
    def extract_molecule_info(self) -> Task:
        return Task(
            config=self.tasks_config["extract_molecule_info"],
            output_pydantic=Experiments,
        )

    @crew
    def crew(self) -> Crew:
        """Creates the Extractor crew"""
        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            process=Process.sequential,
            verbose=True,
        )
