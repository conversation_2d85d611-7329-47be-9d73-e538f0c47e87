ExperimentExtractor_task:
  description: >
    Analyze the information in the following paper and extract #文章中对entity做的所有的实验方法和该实验的结论
    Extract the following:
    - Experiment
      * Co-localization:such as Immunofluorescence experiment
      * In vivo droplet formation (live-cell imaging)
      * In vivo FRAP
      * In vitro droplet formation (turbidity/microscopy)
      * In vitro FRAP
      * Regulatory mechanism validation: Such as Domain truncation/deletion, Site-directed mutagenesis, Overexpression/knockdown, Post-translational modifications, Partner molecule interaction (RNA, proteins, etc.)
    - Experiment Note: Experiment Conclusion
    Attention
    - Include ALL instances of repeated experiment types (e.g., three separate FRAP assays → three entries)
    Here is the paper info:
    {paper_info}
  expected_output: >
    A json object of biomolecules that undergo phase separation with the following format:
    {
      "Experiment_Evidence": [
        {
          "Name": "Entity Name",
          "Experiments": [
            {
              "Experiment": "Experiment type",
              "Experiment Note": "Experimental Conclusion of the experiment",
            },
            // Repeat for ALL experimental instances
          ]
        }
      ]
      
    }
  agent: ExperimentExtractor
