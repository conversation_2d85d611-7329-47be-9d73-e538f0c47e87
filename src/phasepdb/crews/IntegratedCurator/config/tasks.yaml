task_intake:
  description: >
    1. Read the provided scientific paper text .\n
    2. Determine if the paper presents PRIMARY EXPERIMENTAL DATA (not just review/theory) investigating protein phase separation. Output 'is_relevant: True' or 'is_relevant: False'.\n
    3. Identify the primary protein(s) investigated for phase separation (use gene symbol or common name). \n
    5. Identify the organism studied.\n
    6. Provide a brief (1-2 sentence) justification for relevance, mentioning key experimental approaches used (e.g., 'Presents in vitro reconstitution and cellular imaging...').
    This is the full paper text: {paper_full_text}
  expected_output: >
    A structured report containing:\n
    - is_relevant: True/False\n
    - focus_proteins: List of {'name': String}\n
    - organism: {'name': String}\n
    - relevance_summary: String

task_analyze:
  description: >
    Using the full paper text ({paper_full_text}) and the focus protein(s)/organism identified previously, perform a detailed analysis.\n
    Extract EXPERIMENTAL evidence for each focus protein regarding:\n
    - Phase Separation Observation: In vivo methods/findings? In vitro methods/findings (incl. crowding agents)? Condensate identity?\n
    - Role in Condensate: Evidence for Scaffold/Client/Regulator?\n
    - Driving Force: Evidence for LCD/Domains/Oligomerization? Key regions/mutations and their effects?\n
    - Material Properties: Evidence for Liquid/Solid state? FRAP/Fusion/Morphology data?\n
    - Functional Relevance: Linked biological process? Strength of experimental link (mutants, rescue?)\n
    - Regulation: Reported regulatory mechanisms (PTMs, stress, partners)?\n
    **CRITICAL**: For every point extracted, cite the specific evidence from the paper (e.g., 'Fig 2A shows...', 'Methods state...', 'Results section paragraph 3 claims based on FRAP...', 'Mutation X abolished PS (Fig 4C)'). Be precise and objective.
  expected_output: >
    A comprehensive textual report detailing the experimental findings for each focus protein, organized by the categories above.
    Each statement MUST be explicitly linked to supporting evidence cited from the paper text.

task_format:
  description: >
    Take the detailed experimental evidence report provided in the context.
    Format this information STRICTLY according to the following JSON schema:
    ```json
    {json_schema_definition}
    ```
    Ensure all fields from the schema are present. If the evidence report does not provide information for a specific field, use `null` for optional fields or specific strings like 'Not Mentioned' / 'Not Assessed' / 'Not Addressed' / 'Evidence Lacking' where appropriate according to the schema notes.
    Populate the `publication`, `protein_info`, and `organism` sections based on the initial intake report context as well.
    Leave `curation_info` fields (confidence, notes, date) blank for the QC agent to fill.

  expected_output: >
    A single JSON object string that perfectly matches the provided schema structure, containing the extracted information.

task_validate:
  description: >
    Review the generated JSON data string and the detailed evidence report it was based on.
    1. Verify that the JSON accurately reflects the findings reported by the Analyst, without adding interpretations.
    2. Ensure the JSON structure strictly adheres to the required schema.
    3. Critically assess the STRENGTH and CLARITY of the experimental evidence described in the Analyst's report (which cites the paper).
    4. Based on this assessment, assign a `curator_confidence` level ('High', 'Medium', 'Low'). High confidence requires clear, direct experimental evidence for key claims (e.g., PS observation, driving force). Low confidence if evidence is weak, relies heavily on problematic methods (e.g., high overexpression, uncontrolled crowding agents), or is contradictory.
    5. Write concise but informative `curator_notes`. These MUST include specific points justifying the confidence score, mentioning strengths (e.g., 'Strong functional link shown via rescue assay') and weaknesses/caveats (e.g., 'In vitro PS required 10% PEG', 'Role based only on IF co-localization', 'Driving force mutation analysis was limited').
    6. Integrate the confidence and notes into the final JSON output.

  expected_output: >
    The final, validated JSON object string, including the filled `curator_confidence` and `curator_notes` fields within `curation_info`.
