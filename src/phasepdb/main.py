#!/usr/bin/env python
from pydantic import BaseModel
from typing import List
from crewai.flow import Flow, listen, start, router
from crews.curator.curator import Curator, Extractor, Entities, Entity
from crews.judger.judger import Judger
import json
import os

# 避免 crewai 的网络问题
os.environ["OTEL_SDK_DISABLED"] = "true"


class Experiment(BaseModel):
    experiment: str
    detail: str
    conclusion: str
    note: str


class ExpEntities(Entities):
    experiments: List[Experiment]


class FinderState(BaseModel):
    paper_info: str = ""
    relatedness: float = 0.0
    biomolecules: List[Entity] = []  # 使用 Entity 类型替代 list[dict]


class FinderFlow(Flow[FinderState]):
    def __init__(self, path, overwrite=True):
        super().__init__()
        self.path = path
        self.pmid = path.split("/")[-1].split(".")[0]
        self.overwrite = overwrite
        os.makedirs("output", exist_ok=True)

    def _load_cached_data(self, file_path, key=None):
        """尝试加载缓存数据，如果overwrite为True或文件不存在则返回None"""
        if self.overwrite or not os.path.exists(file_path):
            return None

        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)

        return data[key] if key else data

    def _save_data(self, file_path, data):
        """保存数据到指定路径"""
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=4)

    @start()
    def get_paper_info(self):
        print("Getting paper info...")
        with open(self.path, "r", encoding="utf-8") as f:
            self.state.paper_info = f.read()
        print(f"The length of the paper info is {len(self.state.paper_info)}")

    @listen(get_paper_info)
    def get_relatedness(self):
        print("Getting relatedness...")
        relatedness_path = f"output/{self.pmid}_relatedness.json"

        # 尝试加载缓存数据
        cached_data = self._load_cached_data(relatedness_path, "p")
        if cached_data is not None:
            self.state.relatedness = cached_data
            return

        # 如果没有缓存或需要重写，执行计算
        inputs = {"paper_intro": self.state.paper_info[:10000]}
        result = Judger(pmid=self.pmid).crew().kickoff(inputs=inputs)
        self.state.relatedness = result.pydantic.p

    @router(get_relatedness)
    def relatedness_route(self):
        if self.state.relatedness > 0.5:
            return "success"
        else:
            return "failed"

    @listen("success")
    def get_biomolecules(self):
        print("Getting biomolecules...")
        biomolecules_path = f"output/{self.pmid}_biomolecules.json"

        # 尝试加载缓存数据
        cached_data = self._load_cached_data(biomolecules_path, "entities")
        if cached_data is not None:
            # 确保转换为Entity对象
            self.state.biomolecules = [
                Entity.model_validate(mol) if isinstance(mol, dict) else mol
                for mol in cached_data
            ]
            return

        # 如果没有缓存或需要重写，执行计算
        inputs = {"paper_info": self.state.paper_info}
        result = Curator(pmid=self.pmid).crew().kickoff(inputs=inputs)
        self.state.biomolecules = result.pydantic.entities

    @listen(get_biomolecules)
    def extract_molecule_info(self):
        biomolecules_info_path = f"output/{self.pmid}_biomolecules_info.json"

        # 尝试加载缓存数据
        cached_data = self._load_cached_data(biomolecules_info_path)
        if cached_data is not None:
            self.state.biomolecules = [
                Entity.model_validate(mol) for mol in cached_data
            ]
            return

        # 如果没有缓存或需要重写，执行计算
        for i, mol in enumerate(self.state.biomolecules):
            # 确保mol是Entity对象
            if isinstance(mol, dict):
                mol = Entity.model_validate(mol)
                self.state.biomolecules[i] = mol

            result = (
                Extractor(pmid=self.pmid)
                .crew()
                .kickoff(
                    inputs={"molecule": str(mol), "paper_info": self.state.paper_info}
                )
            )

            # 安全地设置experiments属性
            if hasattr(result.pydantic, "experiments"):
                mol.experiments = result.pydantic.experiments

        # 保存结果
        self._save_data(
            biomolecules_info_path,
            [mol.model_dump() for mol in self.state.biomolecules],
        )


def kickoff(path, overwrite=False):
    finder_flow = FinderFlow(path=path, overwrite=overwrite)
    finder_flow.kickoff()


def plot(path):
    finder_flow = FinderFlow(path=path)
    finder_flow.plot()


if __name__ == "__main__":
    input_pmid = "test_input"
    path = f"data/{input_pmid}.md"
    assert os.path.exists(path), f"File not found: {path}"
    plot(path=path)
    kickoff(path=path)
    # kickoff(path="data/test_input_neg.md")
