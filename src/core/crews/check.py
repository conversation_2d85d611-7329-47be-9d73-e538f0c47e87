from crewai import Agent, Crew, Process, Task, LLM
from crewai.project import Crew<PERSON><PERSON>, agent, crew, task
import os
from dotenv import load_dotenv
from pydantic import BaseModel
from typing import List
import json


load_dotenv('key.env')

DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
DEEPSEEK_BASE_URL = os.getenv("DEEPSEEK_BASE_URL")
MODEL = "deepseek-chat"

llm_ark = LLM(
    model=MODEL,
    api_key=DEEPSEEK_API_KEY,
    base_url=DEEPSEEK_BASE_URL,
)

@CrewBase
class LLPS_InformationExtraction:
    """
    Creating 6 information extraction crew
    params:
            pmid: str, paper PMID for file storage
    """

    agents_config = "../config/agents.yaml"
    tasks_config = "../config/tasks.yaml"

    def __init__(self, pmid: str):
        super().__init__()
        self.pmid = pmid

    #Agent
    @agent
    def gene_extractor(self) -> Agent:
        return Agent(
            config=self.agents_config["gene_extractor"],
            verbose=True,
            llm=llm_ark,
            allow_delegation=True
        )
    
    @agent
    def gene_validator(self) -> Agent:
        return Agent(
            config=self.agents_config["gene_validator"],
            verbose=True,
            llm=llm_ark,
            allow_delegation=True
        )
    
    @agent
    def phase_separation_analyst(self) -> Agent:
        return Agent(
            config=self.agents_config["phase_separation_analyst"],
            verbose=True,
            llm=llm_ark,
            allow_delegation=True
        )
    
    @agent
    def Data_Architect(self) -> Agent:
        return Agent(
            config=self.agents_config["Data_Architect"],
            verbose=True,
            llm=llm_ark,
            allow_delegation=True
        )
    
    #Task
    @task
    def gene_extraction_task(self) -> Task:
        return Task(
            config=self.tasks_config["gene_extraction_task"],
            output_file=f"output/{self.pmid}_gene_extraction.md",
        )
    
    @task
    def gene_validation_task(self) -> Task:
        return Task(
            config=self.tasks_config["gene_validation_task"],
            context=[self.gene_extraction_task()],
            output_file=f"output/{self.pmid}_gene_validation.md",
        )
    
    @task
    def information_extraction_task(self) -> Task:
        return Task(
            config=self.tasks_config["information_extraction_task"],
            context=[self.gene_validation_task()],
            output_file=f"output/{self.pmid}_information_extraction.md",
        )
    
    @task
    def data_consolidation_task(self) -> Task:
        return Task(
            config=self.tasks_config["data_consolidation_task"],
            context=[self.information_extraction_task()],
            output_file=f"output/{self.pmid}_data_consolidation.md",
        )
    
    #Crew
    @crew
    def crew(self) -> Crew:
        """Creates the 6 information extraction"""

        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            process=Process.sequential,
            verbose=True,
        )
    


if __name__ == "__main__":
    pmid = "29262335"
    full_text = open(
        f"../input/{pmid}/{pmid}.md", "r", encoding="utf-8"
    ).read()
    inputs = {"paper_info": full_text}
    result = LLPS_InformationExtraction(pmid=pmid).crew().kickoff(inputs=inputs)
    
    # 输出结果到控制台
    print(result.raw)
    
    # 创建输出目录（确保目录存在）
    output_dir = "../json/outputlyx"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 保存为JSON文件
    output_path = f"{output_dir}/{pmid}_result.json"
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(result.raw, f, indent=4, ensure_ascii=False)  # 格式化输出，支持中文



