import os
from dotenv import load_dotenv
from crewai import LLM, Agent

# 加载.env文件
# 获取当前脚本所在目录
current_dir = os.path.dirname(os.path.abspath(__file__)) if '__file__' in globals() else os.getcwd()
env_path = os.path.join(current_dir, 'key.env')
load_dotenv(env_path)


# 获取环境变量
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
DEEPSEEK_BASE_URL = os.getenv("DEEPSEEK_BASE_URL")
ZHIPU_API_KEY = os.getenv('ZHIPU_API_KEY') 
ZHIPU_BASE_URL = os.getenv('ZHIPU_BASE_URL')
ALI_API_KEY = os.getenv('ALI_API_KEY')
ALI_BASE_URL = os.getenv('ALI_BASE_URL')
ARK_API_KEY = os.getenv('ARK_API_KEY')
ARK_BASE_URL = os.getenv('ARK_BASE_URL')

MODEL = "deepseek-chat"
#print(DEEPSEEK_BASE_URL)

# llm = LLM(
#     model="openai/glm-4-flash",
#     api_key=ZHIPU_API_KEY,
#     base_url=ZHIPU_BASE_URL
# )
#llm_ali = LLM(
#    model="openai/qwq-plus",
#    api_key=ALI_API_KEY,
#    base_url=ALI_BASE_URL
#)


llm_ark = LLM(
    model=MODEL,
    api_key=DEEPSEEK_API_KEY,
    base_url=DEEPSEEK_BASE_URL,
)

response = llm_ark.call(
    "Analyze the following messages and return the name, age, and breed. "
    "Meet Kona! She is 3 years old and is a black german shepherd."
)
print(response)

