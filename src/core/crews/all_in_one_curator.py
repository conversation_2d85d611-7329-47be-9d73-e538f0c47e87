from crewai import Agent, Crew, Process, Task, LLM
from crewai.project import Crew<PERSON><PERSON>, agent, crew, task
import os
from dotenv import load_dotenv
from pydantic import BaseModel
from typing import List
import datetime

load_dotenv()
# MODEL = "openai/doubao-1-5-thinking-pro-250415"
PROVIDER = os.getenv("PROVIDER")
MODEL_NAME = os.getenv("MODEL")
MODEL_NAME = "deepseek-chat"
MODEL = f"openai/{MODEL_NAME}"
API_KEY = os.getenv(f"{PROVIDER}_API_KEY")
BASE_URL = os.getenv(f"{PROVIDER}_BASE_URL")

llm = LLM(
    model=MODEL,
    api_key=API_KEY,
    base_url=BASE_URL,
)


class PerProtein(BaseModel):
    gene_name: str
    organism: str
    mlo: List[str]
    mlo_association_summary: str
    location: List[str]
    observed_ps_behavior_summary: str
    key_protein_regions_studied_ps: str
    experiment_types: List[str]
    detailed_experiments_list: List[dict]
    material_state: List[str]
    overall_material_properties_summary: str
    material_state_transition_observed_summary: str
    key_drivers_intrinsic_regulators: dict
    co_phase_separation_partners_extrinsic_regulators: List[dict]
    functional_implications_summary: str
    key_supporting_statements: List[str]
    class_: str


class Proteins(BaseModel):
    proteins: List[PerProtein]


@CrewBase
class LLPSCurator:
    """
    Creating All-in-one Curator crew
    params:
            pmid: str, paper PMID for file storage
    """

    agents_config = "../config/agents.yaml"
    tasks_config = "../config/all-in-one-task.yaml"

    def __init__(self, pmid: str):
        super().__init__()
        self.pmid = pmid

    @agent
    def llps_curator(self) -> Agent:
        return Agent(
            config=self.agents_config["llps_curator"],
            verbose=False,
            llm=llm,
        )

    @agent
    def data_formatter_agent(self) -> Agent:
        return Agent(
            config=self.agents_config["data_formatter_agent"],
            verbose=False,
            llm=llm,
        )

    @task
    def curation_task(self) -> Task:
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        return Task(
            config=self.tasks_config["curation_task"],
            agent=self.llps_curator(),
            output_file=f"output/{self.pmid}_{timestamp}_{PROVIDER}_{MODEL_NAME}.md",
        )

    @task
    def formatting_task(self) -> Task:
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        return Task(
            config=self.tasks_config["formatting_task"],
            agent=self.data_formatter_agent(),
            context=[self.curation_task()],
            output_file=f"output/{self.pmid}_{timestamp}_{PROVIDER}_{MODEL_NAME}.json",
            output_pydantic=Proteins,
        )

    @crew
    def crew(self) -> Crew:
        """Creates the All-in-one Curator crew"""

        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            process=Process.sequential,
            verbose=False,
        )


if __name__ == "__main__":
    # 获取所有PMID目录
    markdown_dir = "data/input/markdown"
    pmid_dirs = [
        d
        for d in os.listdir(markdown_dir)
        if os.path.isdir(os.path.join(markdown_dir, d)) and d.isdigit()
    ]
    pmid_dirs.sort()

    total = len(pmid_dirs)
    success = 0
    failed = 0

    print(f"开始处理 {total} 个文件...")

    for i, pmid in enumerate(pmid_dirs[:1], 1):
        try:
            print(f"[{i}/{total}] 处理 PMID: {pmid}")
            full_text = open(
                f"data/input/markdown/{pmid}/{pmid}.md", "r", encoding="utf-8"
            ).read()
            inputs = {"paper_full_text": full_text}
            result = LLPSCurator(pmid=pmid).crew().kickoff(inputs=inputs)
            success += 1
            print(f"✅ 成功处理: {pmid}")
        except Exception as e:
            failed += 1
            print(f"❌ 处理失败: {pmid} - {str(e)}")

    print(f"\n处理完成！总计: {total}, 成功: {success}, 失败: {failed}")
