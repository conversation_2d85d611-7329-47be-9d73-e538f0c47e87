import os
import time
import threading
import argparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from crews.all_in_one_curator import LLPSCurator


class BatchProcessor:
    def __init__(self, max_workers=5, resume=True):
        self.max_workers = max_workers
        self.resume = resume
        self.results_lock = threading.Lock()
        self.success_count = 0
        self.failed_count = 0
        self.skipped_count = 0
        self.total_count = 0

    def is_already_processed(self, pmid):
        """Check if PMID has already been processed with the current model"""
        from crews.all_in_one_curator import MODEL_NAME

        output_dir = "output"
        if not os.path.exists(output_dir):
            return False

        # Extract model name from the full model path (e.g., "openai/gpt-4o" -> "gpt-4o")
        current_model = MODEL_NAME

        # Check for both curated (.md) and formatted (.json) files with matching PMID and model
        for filename in os.listdir(output_dir):
            # Check for curated files with matching PMID and model
            if (
                filename.startswith(f"{pmid}_")
                and current_model in filename
                and filename.endswith(".md")
            ):
                # Also check for corresponding JSON file with matching model
                json_files = [
                    f
                    for f in os.listdir(output_dir)
                    if (
                        f.startswith(f"{pmid}_")
                        and current_model in f
                        and f.endswith(".json")
                    )
                ]

                if json_files:
                    return True

        return False

    def process_single_pmid(self, pmid, index):
        """Process a single PMID"""
        try:
            # Check if already processed (resume functionality)
            if self.resume and self.is_already_processed(pmid):
                with self.results_lock:
                    self.skipped_count += 1
                    print(
                        f"⏭️  [{index}/{self.total_count}] Skipping already processed: {pmid} (Success: {self.success_count}, Failed: {self.failed_count}, Skipped: {self.skipped_count})"
                    )
                return {
                    "pmid": pmid,
                    "status": "skipped",
                    "reason": "already_processed",
                }

            print(
                f"[{index}/{self.total_count}] Processing PMID: {pmid} (Thread: {threading.current_thread().name})"
            )

            # Read file
            markdown_path = f"data/input/markdown/{pmid}/{pmid}.md"
            if not os.path.exists(markdown_path):
                raise FileNotFoundError(f"File not found: {markdown_path}")

            with open(markdown_path, "r", encoding="utf-8") as f:
                full_text = f.read()

            # Process
            inputs = {"paper_full_text": full_text}
            result = LLPSCurator(pmid=pmid).crew().kickoff(inputs=inputs)

            # Update success count
            with self.results_lock:
                self.success_count += 1
                print(
                    f"✅ [{index}/{self.total_count}] Successfully processed: {pmid} (Success: {self.success_count}, Failed: {self.failed_count}, Skipped: {self.skipped_count})"
                )

            return {"pmid": pmid, "status": "success", "result": result}

        except Exception as e:
            # Update failure count
            with self.results_lock:
                self.failed_count += 1
                print(
                    f"❌ [{index}/{self.total_count}] Failed to process: {pmid} - {str(e)} (Success: {self.success_count}, Failed: {self.failed_count}, Skipped: {self.skipped_count})"
                )

            return {"pmid": pmid, "status": "failed", "error": str(e)}

    def load_pmids_from_file(self, file_path):
        """Load PMIDs from a file (comma-separated or one per line)"""
        with open(file_path, "r") as f:
            content = f.read().strip()

        # Check if content is comma-separated
        if "," in content:
            pmids = [pmid.strip() for pmid in content.split(",")]
        else:
            # Assume one PMID per line
            pmids = [line.strip() for line in content.splitlines() if line.strip()]

        # Filter out non-numeric PMIDs
        valid_pmids = [pmid for pmid in pmids if pmid.isdigit()]

        if len(valid_pmids) < len(pmids):
            print(
                f"⚠️ Warning: {len(pmids) - len(valid_pmids)} invalid PMIDs were filtered out"
            )

        return valid_pmids

    def run_batch(self, pmid_list=None, pmid_file=None):
        """Run batch processing"""
        if pmid_list:
            # Process comma-separated list of PMIDs
            pmid_dirs = [
                pmid.strip() for pmid in pmid_list.split(",") if pmid.strip().isdigit()
            ]
            print(f"📋 Using provided PMID list with {len(pmid_dirs)} entries")
        elif pmid_file:
            # Load PMIDs from file
            if not os.path.exists(pmid_file):
                print(f"❌ PMID file not found: {pmid_file}")
                return
            pmid_dirs = self.load_pmids_from_file(pmid_file)
            print(f"📋 Loaded {len(pmid_dirs)} PMIDs from file: {pmid_file}")
        else:
            # Get all PMID directories
            markdown_dir = "data/input/markdown"
            if not os.path.exists(markdown_dir):
                print(f"❌ Directory not found: {markdown_dir}")
                return

            pmid_dirs = [
                d
                for d in os.listdir(markdown_dir)
                if os.path.isdir(os.path.join(markdown_dir, d)) and d.isdigit()
            ]
            pmid_dirs.sort()
            print(f"📋 Found {len(pmid_dirs)} PMID directories in {markdown_dir}")

        self.total_count = len(pmid_dirs)

        if self.total_count == 0:
            print("❌ No valid PMIDs to process")
            return

        # Check how many are already processed if resume is enabled
        if self.resume:
            already_processed = sum(
                1 for pmid in pmid_dirs if self.is_already_processed(pmid)
            )
            remaining = self.total_count - already_processed
            print(
                f"🔄 Resume mode enabled: {already_processed} already processed, {remaining} remaining"
            )

        print(
            f"🚀 Starting concurrent processing of {self.total_count} files with {self.max_workers} workers"
        )
        print("=" * 60)

        start_time = time.time()

        # Use thread pool for concurrent processing
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_pmid = {
                executor.submit(self.process_single_pmid, pmid, i + 1): pmid
                for i, pmid in enumerate(pmid_dirs)
            }

            # Wait for all tasks to complete
            for future in as_completed(future_to_pmid):
                pmid = future_to_pmid[future]
                try:
                    future.result()  # Get result but don't need to store
                except Exception as e:
                    with self.results_lock:
                        self.failed_count += 1
                        print(f"❌ Thread exception for PMID {pmid}: {str(e)}")

        end_time = time.time()
        elapsed_time = end_time - start_time

        print("\n" + "=" * 60)
        print("🎉 Batch processing completed!")
        print(f"📊 Total: {self.total_count}")
        print(f"✅ Success: {self.success_count}")
        print(f"❌ Failed: {self.failed_count}")
        print(f"⏭️  Skipped: {self.skipped_count}")
        print(f"⏱️  Total time: {elapsed_time:.2f} seconds")
        if self.total_count > 0:
            print(f"📈 Average per file: {elapsed_time / self.total_count:.2f} seconds")
        print("=" * 60)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Batch process PMIDs for LLPS curation"
    )
    parser.add_argument(
        "-w",
        "--workers",
        type=int,
        default=5,
        help="Number of concurrent workers (default: 5)",
    )
    parser.add_argument(
        "--no-resume",
        action="store_true",
        help="Disable resume mode (reprocess all files)",
    )
    parser.add_argument(
        "-p", "--pmids", help="Comma-separated list of PMIDs to process"
    )
    parser.add_argument(
        "-f",
        "--file",
        help="Path to file containing PMIDs (comma-separated or one per line)",
    )

    args = parser.parse_args()

    processor = BatchProcessor(max_workers=args.workers, resume=not args.no_resume)
    processor.run_batch(pmid_list=args.pmids, pmid_file=args.file)
