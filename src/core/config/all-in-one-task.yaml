# By YKQ
# ALL-in-one task
curation_task:
  description: >
    <CONTEXT>
      You are performing a critical data extraction task for the **PhaSepDB V3 project**. This project aims to build a comprehensive and high-quality database of proteins involved in Liquid-Liquid Phase Separation (LLPS) to accelerate scientific research in this field. The information you extract will be directly used by researchers, so **accuracy, exhaustiveness, and strict adherence to experimental findings reported within the provided article are paramount.** You will primarily be analyzing full-text, peer-reviewed **primary research articles** focused on the experimental characterization of protein phase separation. Your analysis should provide a detailed, protein-by-protein account suitable for populating this scientific database.
    </CONTEXT>
    <INSTRUCTION>
      1.  **Thoroughly Read and Analyze:** Carefully read the entire article to identify every protein for which phase separation behavior or properties are experimentally investigated or substantively discussed.
      2.  **Protein-by-Protein Summary:** For **each** such protein identified, create a detailed summary. Organize the summary for each protein clearly. Proteins of different organisms are considered different proteins.
      3.  **Exhaustive Detail Extraction:** Within each protein"s summary, you must extract and present the following information as comprehensively as possible, based *only* on the content of the provided article:

          * **A. Protein Info:**
              * **Protein Name/Identifier:** Clearly state the name or identifier of the protein as used in the article (e.g., FUS, TDP-43).
              * **Organism:** Specify the organism in which the protein was studied or the experiment was performed. Strive for scientific organism names (e.g., "Homo sapiens", "Mus musculus", "Saccharomyces cerevisiae"), don"t use abbreviations. If not explicitly stated, use "Not specified".
              * **Protein Region(s) Involved in PS / Studied Constructs Overview:** Briefly list or describe key protein regions (e.g., "N-terminal IDR aa 1-214", "RGG motifs", "full-length protein", "C-terminal truncation mutant ΔC") explicitly stated *in this article* as being involved in its phase separation or predominantly used across experiments for studying its PS. (Detailed constructs per experiment go in C.ii).

          * **B. Observed Phase Separation Behavior Summary:**
              * **Overall Summary:** Describe the general phase separation behavior observed for this protein as summarized in the article (e.g., "forms liquid droplets readily in vitro and in cells under stress," "undergoes LLPS dependent on RNA concentration," "is recruited to existing condensates and can modulate their properties," "exhibits concentration-dependent liquid-to-solid transitions").
              * **Key Supporting Statement(s):** Extract one or two concise, impactful statements directly from the article (preferably from Results, Discussion, or Abstract) that globally summarize this protein"s phase separation behavior, its significance as reported in *this article*, or a key conclusion regarding its PS.

          * **C. Detailed Experimental Evidence (List each distinct experimental setup/type separately):**
              For *each type of experiment* performed on this protein related to phase separation:
               * **i. Experiment Type:** Clearly name the experimental technique used (e.g., "In Vitro Reconstitution Assay," "Fluorescence Recovery After Photobleaching (FRAP) in cells/in vitro," "Live-cell Imaging of Puncta Formation," "Turbidity Assay," "Sedimentation Assay," "Droplet Fusion Assay," "NMR Spectroscopy," "smFRET," "Mutagenesis Studies targeting IDRs/LCDs," "1,6-Hexanediol Treatment Assay," "Co-localization Studies," "Phase Diagram Determination," "Microscopy observation of condensates"). Note the examples are not exhaustive.
              * **ii. Constructs Used:** Specify all protein constructs used in *this particular experiment* (e.g., "full-length recombinant human Protein X," "GFP-tagged Protein Y (aa 1-150)," "Protein Z with P525L mutation," "N-terminal IDR of Protein A," "RNA-binding mutant"). Include details of tags, truncations, or specific mutations.
              * **iii. Cell Lines / Tissue / System:** If applicable (for *in vivo* experiments conducted within cultured cells or living organism), state the cell line(s) (e.g., "HEK293T cells," "U2OS cells") or tissue used. For *in vitro* experiments (using isolated/purified components), specify if it"s a purified system. (List all if multiple, comma-separated).
              * **iv. Key Experimental Conditions:** Describe all relevant conditions for *this experiment* (e.g., protein concentrations, buffer composition (pH, salt types and concentrations), temperature, presence and concentration of crowding agents (PEG, Ficoll), specific stressors (heat shock, osmotic stress, oxidative stress), presence and nature of interacting partners like RNA (type, concentration), DNA, other proteins, or small molecules/ligands specifically used in *this experimental setup*).
              * **v. Detailed Results:** Summarize the specific quantitative and qualitative results obtained from *this experiment* regarding the protein"s phase separation (e.g., "Droplets formed at protein concentrations above X µM," "FRAP showed Y% recovery with a t½ of Z seconds," "The P123A mutation completely abolished droplet formation," "Droplets were observed to fuse within X minutes," "1,6-hexanediol treatment led to complete dissolution of puncta within Y minutes," "Protein A co-phase separated with RNA B, but not RNA C," "The critical concentration for LLPS shifted from X to Y upon phosphorylation at Ser100").
              * **vi. Authors" Conclusions from this Experiment:** State the conclusions drawn by the authors based on the results of *this specific experiment* concerning the protein"s phase separation. Stick to conclusions explicitly stated in the article, do not infer.

          * **D. Cellular distribution:**
              * **Cellular Location:** Describe the intracellular location of this protein within the cell based on experimental evidence in *this article* (e.g.  Nucleus, Cytoplasm)
              * **MLO Association:** Describe any specific Membraneless Organelle(s) (MLO) (e.g., Stress Granules, P-bodies, Nucleolus, Cajal Body, PML body, nuclear speckle, etc.) this protein is experimentally shown *in this article* to localize to, be a component of, or contribute to the formation of. Note if the protein is described as a marker or a key structural/driver component for a specific MLO based on evidence in *this article*.

          * **E. Overall Material Properties & Transitions:**
              * **Overall Material Properties:** Describe discussion about the material properties of the condensates formed by this protein (e.g., "liquid-like," "gel-like," "solid-like," "viscoelastic," "dynamic," "stable," "anisotropic structure with core-shell") based on experimental evidence *in this article* if possible. If not explicitly stated, use "Not specified". These properties are typically inferred from experimental evidence. Look for discussions in the article that link experimental outcomes to material state conclusions. For example:
                  * Molecular Mobility (e.g., from FRAP, FLIP): Rapid fluorescence recovery often indicates a "liquid-like" state, while slow or incomplete recovery may suggest "gel-like" or "solid-like" properties.
                  * Droplet Fusion: The ability of droplets to fuse is a hallmark of "liquid-like" behavior. Lack of fusion can point to more viscous or solid states.
                  * Morphology and Shape: Spherical droplets are characteristic of liquids. Changes in shape over time (e.g., to fiber-like structures) can signal material state transitions.
                  * Viscoelasticity (e.g., from Microrheology): Measurements of probe diffusion can describe how "liquid-like" (low viscosity) or structured the condensate is.
                  * Surface Tension: Assays like right-angle imaging or surface wetting can inform about the liquid properties and stability of the condensate.
                  * Response to Perturbations: Condensate dissolution by agents like 1,6-hexanediol (interpret with caution ) or sensitivity to changes in temperature, salt, or pH can also inform about their material nature and stability.
                  * Structural Analysis (e.g., from Cryo-EM): For less dynamic states, structural details (like cross-β formations) can identify "solid-like" or amyloid-like characteristics.
              * **Droplet State Transition Observed:** Note whether the article explicitly reports any changes or transitions in the material state of the condensates over time, under different conditions, or due to specific modifications. Explicitly state if any transition in droplet material state (e.g., liquid-to-gel, liquid-to-solid, aging effects) was reported for this protein in the article (e.g., "Yes, liquid-to-solid transition observed upon prolonged incubation at high concentration" or "No, material state remained liquid under all tested conditions" or "Not specified").

          * **F. Key Drivers and Intrinsic Regulators of Phase Separation (e.g., PTMs, Mutations, Domains):**
              * Identify and describe any specific intrinsic features of the protein that the article experimentally demonstrates as driving, modulating, or regulating this protein"s phase separation behavior: Specific regions (e.g., IDRs, RGG domain, motifs like IDRs, LCDs, PrLDs essential for phase separation), repeats (e.g. polyQ, dipeptide repeats mediate phase separation), oligomerization (e.g. NTD mediates oligomerization of Par3 which are essential for PS), **Post-Translational Modifications (PTMs)** (type and site if specified, e.g., "phosphorylation at Ser123"), alternative splicing (e.g. hnRNPDL phase separation is regulated by alternative splicing), or **Mutations** (e.g., "P525L missense mutation", "ΔIDR deletion") that the article experimentally demonstrates as driving, modulating, or regulating this protein"s phase separation behavior. Describe the feature and the effect (e.g., "phosphorylation at Ser123 inhibited LLPS", "P525L mutation accelerated aggregation", "Not specified").
              * For each feature/regulator type, specify the exact identity and summaries its regulatory role.

          * **G. Co-phase Separation Partners & Extrinsic Regulators:**
              * Identify all external molecules reported *in this article* to co-phase separate with this protein, to be essential for its PS, or to significantly modulate its PS based on *experimental evidence*.
              * For each  partner/regulator:
                  * **Partner/Regulator Type:** (e.g., Protein, RNA, DNA, Small Molecule/Chemical, Ion).
                  * **Specific Partner/Regulator:** List name/identifier (e.g., for proteins: gene names; for RNA: type, name, or sequence motifs if specified; for chemicals: their names).
                  * **Role in Condensate/Effect on PS (if described):** e.g., Scaffold, Client, Modulator, Essential for formation, Enhances PS, Inhibits PS, Alters droplet properties, Co-localizes within condensates.
                  * **Experimental Evidence for Interaction/Modulation:** Briefly describe the experimental evidences from *this article* supporting their co-participation or regulatory role in phase separation with the primary protein (e.g., "Co-immunoprecipitation from purified condensates," "Observed together in in vitro reconstituted droplets with altered characteristics," "FRAP of partner shows similar dynamics within the same condensate," "Addition of RNA X induced droplet formation of Protein Y").

          * **H. Functional Implications of Phase Separation:**
              * Summarize any experimentally supported functional consequences or roles of this protein"s phase separation discussed *in this article* (e.g., "Phase separation of Protein X was shown to be essential for sequestering Y," "Condensate formation by Protein Z enhances its enzymatic activity by concentrating substrates," "Aberrant phase separation due to mutation M leads to pathological aggregation").
              
      * If a specific piece of information for a field is not mentioned in the article, use the value "Not specified".
      * Adhere to the specified data types and controlled vocabularies where indicated.
      * If the article discusses multiple proteins undergoing phase separation create a separate record for each.
      * Pay close attention to details in Methods, Results, and Figure/Table legends.
      * The goal is to generate data that is directly usable for populating the PhaSepDB V3 database.
    </INSTRUCTION>
    <EXAMPLE>
      **Protein: FUS**

      * **A. Protein Info:**
          * Protein Name/Identifier: FUS
          * Organism: Homo sapiens
          * Protein Region(s) Involved in PS / Studied Constructs Overview: Primarily full-length FUS and various constructs involving its N-terminal Prion-like Domain (PLD) and C-terminal RGG domains were investigated for PS.
      * **B. Observed Phase Separation Behavior:** 
          * **Overall Summary:** FUS readily undergoes LLPS in vitro forming liquid droplets and is recruited to dynamic stress granules in cells, with potential for maturation into more solid-like states under certain conditions or with disease-associated mutations.
          * **Key Supporting Statement(s):** 
              * "Our findings demonstrate that FUS autonomously undergoes LLPS through multivalent interactions mediated by its PLD and RGG domains."
              * "The study suggests that disruptions in the dynamic regulation of FUS phase separation are a key factor in ALS pathogenesis."
      * **C. Detailed Experimental Evidence:**
          * **Experiment 1: In Vitro Reconstitution and Droplet Characterization**
              * Constructs Used: Full-length recombinant human FUS (untagged), GFP-FUS
              * Key Conditions: 10 µM FUS in 50 mM Tris-HCl pH 7.5, 150 mM KCl, 1 mM DTT, 10% PEG, at 25°C
              * Detailed Results: Spherical droplets ~1-5 µm in diameter observed within minutes. Droplets fused upon contact. FRAP analysis showed ~90% recovery with t½ of 8s.
              * Authors" Conclusions: Purified FUS undergoes LLPS in vitro, forming dynamic liquid-like droplets.
          * **Experiment 2: Live-cell Imaging under Stress**
              * Constructs Used: mCherry-FUS
              * Cell Lines / Tissue / System: HeLa cells
              * Key Conditions: Cells treated with 0.5 mM sodium arsenite for 30 minutes.
              * Detailed Results: mCherry-FUS rapidly re-localized from the nucleus to form cytoplasmic granules. These granules co-localized with G3BP1, a stress granule marker.
              * Authors" Conclusions: FUS is recruited to and forms part of stress granules in response to oxidative stress.

      * **D. Cellular distribution:**
          * **Cellular Location:** Nucleus, Cytoplasm
          * **MLO Association:** Reported to be a component of Stress Granules under arsenite stress. 
      
      * **E. Overall Material Properties & Transitions:**
          * **Overall Material Properties:** Fast recovery of fluorescence in FRAP experiments suggests a liquid-like state. 
          * Droplet State Transition Observed: Yes, the article mentions that FUS condensates can age over time or with specific ALS-linked mutations (e.g., G156E) to form more hydrogel-like or solid structures, evidenced by significantly slower FRAP recovery and altered morphology in such cases.

      * **F. Key Drivers and Intrinsic Regulators of Phase Separation:**
          * Domains: The N-terminal Prion-like Domain (PLD, aa 1-214) and C-terminal RGG domains (specifically RGG1, RGG2, RGG3) are experimentally demonstrated to be essential for FUS LLPS. 
          * Repeats: The RGG repeats within the C-terminal domain are crucial for FUS"s phase separation behavior.
          * Oligomerization: Not specified.
          * Alternative Splicing: Not specified.
          * PTMs: Arginine methylation within RGG domains by PRMT1 was shown to reduce FUS"s phase separation propensity and alter its interaction with RNA.
          * Mutations: The G156E mutation in the PLD accelerated FUS aggregation and reduced droplet dynamics.
      * **G. Co-phase Separation Partners & Extrinsic Regulators:**
          * **Partner/Regulator Type:** Protein
              * Partner/Regulator: hnRNPA1
              * Role in Condensate/Effect on PS: Co-assembles with FUS into shared condensates.
              * Experimental Evidence for Interaction/Modulation: Purified hnRNPA1 and FUS when mixed together formed droplets containing both proteins, confirmed by dual-color fluorescence microscopy. Co-expression studies showed TAF15 puncta also contained FUS.
          * **Partner/Regulator Type:** Protein
              * Partner/Regulator: TAF15
              * Role in Condensate/Effect on PS: TAF15 can recruit FUS to its condensates.
              * Experimental Evidence for Interaction/Modulation:  Co-expression studies showed TAF15 puncta also contained FUS.
          * **Partner/Regulator Type:** RNA
              * Partner(s)/Regulator(s): poly(A) RNA, total cellular RNA extracts.
              * Role in Condensate/Effect on PS: Significantly lowers the saturation concentration for FUS LLPS; promotes droplet formation at lower FUS concentrations; can alter droplet viscosity.
              * Experimental Evidence for Interaction/Modulation: In vitro reconstitution showed FUS formed droplets at 1 µM in the presence of 10 ng/µL poly(A) RNA, whereas >10 µM FUS was needed without RNA.
      * **H. Functional Implications of Phase Separation:**
          * Phase separation of FUS into stress granules is implicated in the sequestration and regulation of specific mRNAs during cellular stress responses. Pathological, irreversible FUS aggregation linked to Amyotrophic Lateral Sclerosis (ALS) may originate from aberrant LLPS and dysregulated liquid-to-solid transitions of FUS condensates.
          

      --- *(Separator for the next protein, if any)* ---
    </EXAMPLE>
    <RECAP>
      **Important Considerations:**
      * **Exhaustiveness:** Strive to capture all relevant experimental details for each protein discussed in the context of phase separation. Do not omit details if they are present in the article.
      * **Focus on *This* Article:** Base your summary *solely* on the information presented within the provided scientific article. Do not infer information from external knowledge or other papers or cited papers in the article.
      * **Clarity and Precision:** Use clear language and be precise in your summary of experimental details, results, and conclusions.
      * **No Information:** If specific information for a sub-category is not present in the article for a given protein, state "Not described in this article" or "Not specified" for that sub-category. Don"t make assumptions or make up information.
      * **All Proteins:** Ensure you attempt to provide this level of detail for *every* protein for which such experimental data is available in the article, not just the primary ones.
    </RECAP>
    <INPUT>
      A full-text scientific research article, including abstract, introduction, methods, results, discussion, figure legends, and any supplementary information.
      {paper_full_text}
    </INPUT>
  expected_output: >
    Provide the output as a structured textual summary. Use clear headings for each protein and sub-headings for the different categories of information (A-H above, and i-vi within C). Use bullet points for lists where appropriate to ensure readability and detail.
  agent: llps_curator

formatting_task:
  description: >
    <CONTEXT>
      You are a specialized Data Transformation AI for the **PhaSepDB V3 project**. Your input is a detailed textual summary for one or more proteins,  detailing experimental findings on their phase separation (PS) from a scientific article. Your critical role is to meticulously parse this textual information and convert it into a structured **JSON format**. This JSON output must accurately map the curated details to predefined fields, including specific controlled vocabularies for compatibility with previous database versions (V2) and for downstream accuracy validation. **You must not perform external lookups**. Focus solely on transforming the provided text.
    </CONTEXT>
    <INSTRUCTION>
      1.  **Receive Input:** Take the structured textual summary output from the "llps_curator" agent (organized by protein, with sections A-H) as your input.
      2.  **Process Each Protein Summary:** Generate a entry record for each protein described in the input text in the target json format. Isoforms, mutatants, and modifications of the same protein should be included in single entry. Proteins of different organisms are considered different proteins.  
          * Create a single JSON object representing that protein"s curated data.
          * Extract, infer, and map information from the textual summary to the specified JSON fields below.
          * Aggregate information where necessary (e.g., list all unique cell lines from multiple experiments).
          * Apply controlled vocabularies strictly for fields like `location`, `experiment_types`, `material_state`, and `class`.

      **Target JSON Structure and Mapping Guidance per Protein:**

      * `gene_name`: (String) Infer from input section `A.Protein Name(s)/Identifier(s)`. Record canonical gene name ONLY.
            ***attention***: 
              1. Don"t create separate entries for variants referred like ("GFP-Axin (human Axin isoform a, C-terminally tagged)" → "Axin", "D1 peptide (residues 287-322 of TDP-43)" → "TDP-43")
      * `organism`: (String) Infer from input section `A.Organism`.
            * Restrict all organism names into scientific naming (e.g., "human" → "Homo sapiens")
            * Keep the organism name ONLY, other information such as cell line/tissue specifiers are not needed.
      * `mlo`: (List of Strings) Associated Membrane-less Organelles mapped from input section `D.MLO Association`
            * Predefined MLO List (select all applicable from this list only): 
              ["Cajal body", "DNA damage foci", "enhancer condensate", "Heterochromatin", "Histone locus body",
                "Mediator condensate", "Nuclear body", "Nuclear dicing body", "Nuclear pore complex", "Nuclear speckle",
                "Nuclear stress body", "Nucleolus", "Paraspeckle", "PML nuclear body", "Polycomb body", "Sam68 nuclear body",
                "Splicesome", "Transcriptional condensate", "Adhesin nanodomain", "Anisosome", "Centrosome",
                "cGAS-DNA complex", "Glycolytic body", "Golgin condensate", "Htt inclusion", "Hyperosmotic shock foci",
                "IMP1 ribonucleoprotein granule", "Inclusion body", "Mutator foci", "Negri body", "Neuronal granule",
                "p62 cluster", "Par complex", "P-body", "Pericentriolar matrix", "Polarisome", "Postsynaptic density",
                "Pre-autophagosomal structure", "Presynaptic active zone condensate", "SARS-CoV-2 N condensate",
                "SINT-speckle", "Spindle apparatus", "STING phase-separator", "Stress granule", "Synapsin condensate",
                "Tau condensate", "UBQLN puncta", "Yb body", "ZO protein compartment", "Balbiani body", "Germ plasm",
                "miRISC", "Nuage", "P granule", "Proteasome containing foci", "Receptor cluster", "Rosenthal fiber",
                "SMN complex", "Z granule"]. 
            * If the mentioned MLO(s) are not within the predefined List, return "Other".
            * Return ["NA"] if no specific organelle association is stated.
            * Allow multiple entries when described in different contexts.
      * `mlo_association_summary`: (String) The descriptive text from input section `D.MLO Association`.
      * `location`: (List of Strings) Infer from input `D.MLO Association`. Allowed values: "Nucleus", "Cytoplasm", "NA".
            * If keywords like "nucleus", "nuclear", "nucleoli" are present, include "Nucleus".
            * If keywords like "cytoplasm", "cytoplasmic", or names of typically cytoplasmic MLOs (e.g., "P-bodies", "Stress Granules") are present, or explicitly described in cytoplasmic compartments, include "Cytoplasm".
            * If both types of keywords are present, include both "Nucleus" and "Cytoplasm".
            * If no clear nuclear or cytoplasmic localization is described in the MLO association, or if MLO association is "Not specified", output `["NA"]`.
      * `observed_ps_behavior_summary`: (String) From input section `B.Observed Phase Separation Behavior (Overall Summary)`.
      * `key_protein_regions_studied_ps`: (String) From input section `A.Protein Region(s) Involved in PS / Studied Constructs Overview`.
      * `experiment_types`: (List of Strings) Analyze each experiment described in input section `C`. Map the `C.i.Experiment Type` (original text) to the following controlled vocabulary: "in vitro droplet formation", "in vivo droplet formation", "in vitro FRAP", "in vivo FRAP". Select all types that apply.
            * The definitions of each experiment type are:
              ["in vitro droplet formation", "in vivo droplet formation","in vitro FRAP", "in vivo FRAP"].
              - "in vitro droplet formation": If the experiments indicates droplet/condensate/assembly formation using purified/isolated components in vitro (e.g. reconstruction experiment).
              - "in vivo droplet formation": If the experiments indicates puncta/granule/droplet formation/existence in cells or living organisms.
              - "in vitro FRAP": If FRAP experiment is conducted in vitro and indicated phase separation properties.
              - "in vivo FRAP": If FRAP experiment is conducted in vivo (in cultured cells or living organisms) and indicated phase separation properties.
      * `detailed_experiments_list`: (List of Objects) Each object represents a distinct experiment from input section `C`. Each object should contain:
            * `experiment_type_original`: (String) The original free text from input `C.i.Experiment Type`.
            * `constructs_used`: (String) From input `C.ii.Constructs Used`.
            * `cell_lines_organism_system`: (String) From input `C.iii.Cell Lines / Organism / System`.
            * `key_experimental_conditions`: (String) From input `C.iv.Key Experimental Conditions`.
            * `detailed_results`: (String) From input `C.v.Detailed Results`.
            * `authors_conclusions_from_experiment`: (String) From input `C.vi.Authors" Conclusions from this Experiment`.
      * `material_state`: (List of Strings) Analyze each experiment described in input section `E.Overall Material Properties & Transitions` to the following controlled vocabulary. 
            * material_state controlled vocabulary: 
              ["liquid", "hydrogel", "solid"].
            * If "liquid-like" or "liquid" is mentioned, include "liquid".
            * If "hydrogel" or "gel-like" is mentioned, include "hydrogel".
            * If "solid-like" or "solid" (in context of PS condensates, not just general aggregation) is mentioned, include "solid".
            * Collect all applicable states. If none explicitly match, this can be an empty list or `["Not specified"]`.
      * `overall_material_properties_summary`: (String) From input section `E.Overall Material Properties`.
      * `material_state_transition_observed_summary`: (String) The descriptive text from input section `E.Droplet State Transition Observed`.
      * "key_drivers_intrinsic_regulators": (Dict of Objects) Describe the intrinsic /features/regulators from input section `F.Key Drivers and Intrinsic Regulators of Phase Separation`. For each feature/regulator type, specify the exact identity and summaries its regulatory role. The types of object are:
          * "domain": Regions/domains/motifs in protein that are driving, contributing to or regulating phase separation. if not described return "Not specified".
          * "repeat": When drivers repeats are referred in context.(e.g. "repeats in hCTD strengthened phase separation"), if not described return "Not specified".
          * "oligomerization" oligomerization that driving, contributing to or regulating phase separation.(e.g. "Oligomerization and prion-like domains of TDP-43 are required for anisosome formation."), if not described return "Not specified".
          * "splicing": Alternative splicing that contributes to phase separation will be included. (e.g. "FXR1 alternative splicing is pronounced in the serine- and arginine-rich intrinsically disordered domain"), if not described return "Not specified".
          * "PTM": Modifications that driving, contributing to or regulating phase separation, if not described return "Not specified".
          * "mutations": Mutations that driving, contributing to or regulating phase separation.(e.g. "p.P481R,p.A465T,p.I634A"), if not described return "Not specified".
      * `co_phase_separation_partners_extrinsic_regulators`: (List of Objects) Describe the extrinsic regulators from input section `F.Co-phase Separation Partners & Extrinsic Regulators`. For each partner/regulator type, list name/identifier (e.g., for proteins: gene names; for RNA or DNA: type, name, or sequence motifs if specified; for chemicals: their names), describe their role in Condensate/Effect on PS (if described, e.g., Scaffold, Client, Modulator, Essential for formation, Enhances PS, Inhibits PS, Alters droplet properties, Co-localizes within condensates.) and associated experimental evidence for interaction/modulation. The object format is "{type: String, name: String, description: String}", e.g. {"type":"proten", "name":"FUS", "description":"A is essential for phase separation of B as observed in in vitro reconstitution assay."}
      * `functional_implications_summary`: (String) From input section `H.Functional Implications of Phase Separation`.
      * `key_supporting_statements`: (List of Strings) From input section `B.Key Supporting Statement(s) on Phase Separation`.
      * `class`: (String) Infer based on evidence. Allowed values: "PS-self", "PS-other".
            * Assign "PS-self" if the input summary provides clear evidence of the protein forming condensates *in vitro droplet formation* **by itself** (i.e., an "in vitro droplet formation" or similar experiment where the `key_experimental_conditions` for that experiment in section `C.iv` primarily list the purified protein and buffer components, and section `F` does not indicate partners are *essential* for this *in vitro* self-assembly).
            * Assign "PS-other" for all other cases (e.g., phase separation observed only *in vivo*/*in cellulo*, or *in vitro* phase separation explicitly requires co-factors/partners as per sections `C.iv` or `F`).

      3.  **Final Output Structure:** If the input text contains summaries for multiple proteins, the final output should be a JSON list, where each element is the JSON object for a single protein. If only one protein, output a list containing that single JSON object. If the input text from `llps_curator` indicates "No relevant PS information found" or is empty, output an empty list `[]`.
    </INSTRUCTION>
    <EXAMPLE_INPUT_FORMAT_REMINDER>
      // This task receives detailed textual input that looks like the <EXAMPLE> output of the "llps_curator" agent"s "curation_task". //
      // For instance: //
      // **Protein: FUS**
      // * **A. Protein Info:**
      //     * Protein Name(s)/Identifier(s): FUS
      //     * Organism(s): Homo sapiens
      //     * Protein Region(s) Involved in PS / Studied Constructs Overview: Primarily full-length FUS...
      // * **B. Observed Phase Separation Behavior Summary:** FUS readily undergoes LLPS...
      // ... (all other sections C through H) ...
      // --- (Separator for the next protein, if any) --- //
    </EXAMPLE_INPUT_FORMAT_REMINDER>

  expected_output: >
    A JSON list, where each element is a JSON object representing one protein. Each JSON object must conform to the structure and fields detailed above, without the triple backticks ```.
    Expected output example:
    ```json
    { "proteins": [
      {
        "gene_name": "FUS",
        "organism": "Homo sapiens",
        "mlo": ["Stress granule"]
        "mlo_association_summary": "Reported to be a core component of Stress Granules under arsenite stress. Also localizes to nuclear gems.",
        "location": ["Nucleus", "Cytoplasm"],
        "observed_ps_behavior_summary": "FUS readily undergoes LLPS in vitro forming liquid droplets and is recruited to dynamic stress granules in cells, with potential for maturation into more solid-like states under certain conditions or with disease-associated mutations.",
        "key_protein_regions_studied_ps": "Primarily full-length FUS and various constructs involving its N-terminal Prion-like Domain (PLD) and C-terminal RGG domains were investigated for PS.",
        "experiment_types": ["in vitro droplet formation", "in vivo droplet formation"],
        "detailed_experiments_list": [
          {
            "experiment_type_original": "In Vitro Reconstitution and Droplet Characterization",
            "constructs_used": "Full-length recombinant human FUS (untagged), GFP-FUS",
            "cell_lines_organism_system": "Purified system",
            "key_experimental_conditions": "10 µM FUS in 50 mM Tris-HCl pH 7.5, 150 mM KCl, 1 mM DTT, 10% PEG, at 25°C",
            "detailed_results": "Spherical droplets ~1-5 µm in diameter observed within minutes. Droplets fused upon contact. FRAP analysis showed ~90% recovery with t½ of 8s.",
            "authors_conclusions_from_experiment": "Purified FUS undergoes LLPS in vitro, forming dynamic liquid-like droplets."
          },
          {
            "experiment_type_original": "Live-cell Imaging under Stress",
            "constructs_used": "mCherry-FUS",
            "cell_lines_organism_system": "HeLa cells",
            "key_experimental_conditions": "Cells treated with 0.5 mM sodium arsenite for 30 minutes.",
            "detailed_results": "mCherry-FUS rapidly re-localized from the nucleus to form cytoplasmic granules. These granules co-localized with G3BP1, a stress granule marker.",
            "authors_conclusions_from_experiment": "FUS is recruited to and forms part of stress granules in response to oxidative stress."
          }
        ],
        "material_state": ["liquid", "hydrogel", "solid"],
        "overall_material_properties_summary": "Initially liquid-like.",
        "material_state_transition_observed_summary": "Yes, the article mentions that FUS condensates can age over time or with specific ALS-linked mutations (e.g., G156E) to form more hydrogel-like or solid structures, evidenced by significantly slower FRAP recovery and altered morphology in such cases.",
        "class": "PS-self",
        "key_drivers_intrinsic_regulators": {
            "domain": "The N-terminal Prion-like Domain (PLD, aa 1-214) and C-terminal RGG domains (specifically RGG1, RGG2, RGG3) are experimentally demonstrated to be essential for FUS LLPS.",
            "repeat": "Not specified",
            "oligomerization": "Not specified"
            "splicing": "FXR1 alternative splicing is pronounced in the serine- and arginine-rich intrinsically disordered domain",
            "PTM": "phosphorylation",
            "mutation" "p.P481R,p.A465T,p.I634A supress the phase separation of FUS",
            },
        "co_phase_separation_partners_extrinsic_regulators": [
          {"type":"protein", "name":"hnRNPA1", "description":"Co-assembles with FUS into shared condensates."},
          {"type":"RNA", "name":"rRNA", "description":"rRNA co-phase separates with FUS."}
        ],
        "functional_implications_summary": "Phase separation of FUS into stress granules is implicated in the sequestration and regulation of specific mRNAs during cellular stress responses. Pathological, irreversible FUS aggregation linked to Amyotrophic Lateral Sclerosis (ALS) may originate from aberrant LLPS and dysregulated liquid-to-solid transitions of FUS condensates.",
        "key_supporting_statements": [
            "Our findings demonstrate that FUS autonomously undergoes LLPS through multivalent interactions mediated by its PLD and RGG domains.",
            "The study suggests that disruptions in the dynamic regulation of FUS phase separation are a key factor in ALS pathogenesis."
        ]
      }]
    }
    ```
  agent: data_formatter_agent
