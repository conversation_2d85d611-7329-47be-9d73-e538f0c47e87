#Task1
gene_extraction_task:
  description: >
    Analyze the information in the following paper and extract all biomolecules mentioned that undergo phase separation.
    Extract the following:
    - name
    - abbreviation
    - organism of the biomolecules
    - the type of molecules:
      * protein
      * DNA
      * RNA
    Attention
    - Each molecule from a specific organism should be recorded as a separate entry
    - All eligible molecules must be returned, even if there are multiple
    - If there are no relevant molecules, return an empty list
    Here is the paper info:
    {paper_info}
  expected_output: >
    A json object of biomolecules that undergo phase separation with the following format:
    {
      "entities": [
        {
          "name": "biomolecule name",
          "abbreviation": "abbreviation",
          "organism": "organism name",
          "molecule_type": "protein, DNA, RNA",
        }
      ]
    }
  agent: finder

  agent: gene_extractor

#Task2
gene_validation_task:
  description: >
    Validate phase separation gene associations and supplement gene names for proteins:
    
    Core Validation Steps:
    1. **Reality Check**: Confirm each entity is explicitly mentioned in {paper_info}
    2. **Evidence Verification**: Identify experimental evidence types supporting phase separation
    3. **Gene Name Enhancement**: Add gene names for protein molecules
    
    Evidence Types to Detect:
    - in_vivo_static: Static imaging evidence (e.g., "foci", "granules")
    - in_vivo_colocalization: Co-localization with markers (e.g., "co-localizes with G3BP1")
    - in_vivo_dynamic: Dynamic behaviors (e.g., "fusion", "fission")
    - in_vitro_droplets: In vitro droplet formation (e.g., "purified protein formed droplets")
    - in_vitro_FRAP: In vitro FRAP evidence (e.g., "fluorescence recovery after photobleaching")
    
    Processing Rules:
    - Remove entries with NO evidence of phase separation
    - Add evidence_types array to each valid entry
    - For proteins: supplement gene_name using abbreviation or text extraction
    - Use "UNKNOWN" when gene name isn't explicitly mentioned
    
    Inputs:
    - Raw text: {paper_info}
    - Extraction results: {gene_extraction_task.output}

  expected_output: >
    Enhanced JSON with evidence types and gene names:
    {
      "entities": [
        {
          "name": "FUS protein",
          "abbreviation": "FUS",
          "organism": "Homo sapiens",
          "molecule_type": "protein",
          "gene_name": "FUS",  // New field
          "evidence_types": [  // Evidence types found
            "in_vitro_droplets",
            "in_vivo_dynamic"
          ]
        }
      ],
      "validation_stats": {
        "total_initial_entries": 15,
        "validated_entries": 12,
        "removed_entries": 3
      }
    }

  agent: gene_validator

#Task3
information_extraction_task:
  description: >
    Extract phase separation characteristics for a specified gene from given text content.
    Required Fields:
    - Location: Subcellular localization
      * Return "Cytoplasm" if explicitly described in cytoplasmic compartments
      * Return "Nucleus" for nuclear localization mentions
      * Return "_" if no definitive localization evidence exists
      * Flag conflicts when contradictory descriptions appear (e.g., "mainly nuclear" vs. "cytoplasmic foci in Fig3")
    - MLO: Membrane-less Organelle
      * MLO outputs are restricted in list below:
        ["Cajal body", "DNA damage foci", "enhancer condensate", "Heterochromatin", "Histone locus body",
          "Mediator condensate", "Nuclear body", "Nuclear dicing body", "Nuclear pore complex", "Nuclear speckle",
          "Nuclear stress body", "Nucleolus", "Paraspeckle", "PML nuclear body", "Polycomb body", "Sam68 nuclear body",
          "Splicesome", "Transcriptional condensate", "Adhesin nanodomain", "Anisosome", "Centrosome",
          "cGAS-DNA complex", "Glycolytic body", "Golgin condensate", "Htt inclusion", "Hyperosmotic shock foci",
          "IMP1 ribonucleoprotein granule", "Inclusion body", "Mutator foci", "Negri body", "Neuronal granule",
          "p62 cluster", "Par complex", "P-body", "Pericentriolar matrix", "Polarisome", "Postsynaptic density",
          "Pre-autophagosomal structure", "Presynaptic active zone condensate", "SARS-CoV-2 N condensate",
          "SINT-speckle", "Spindle apparatus", "STING phase-separator", "Stress granule", "Synapsin condensate",
          "Tau condensate", "UBQLN puncta", "Yb body", "ZO protein compartment", "Balbiani body", "Germ plasm",
          "miRISC", "Nuage", "P granule", "Proteasome containing foci", "Receptor cluster", "Rosenthal fiber",
          "SMN complex", "Z granule"]
      * Return ["_"] if no specific organelle association is stated.
      * Allow multiple entries when described in different contexts.

    - Material State: physical phase state
      * Return "liquid" if description of protein indicating fluidity (fusion, dripping, coalescence).
      * Return "hydrogel" if descriptions of protein are viscoelastic/gel-like states.
      * Return "solid" if mentioned as aggregates/crystalline structures.
      * Return "_" if there is no obvious description in physical phase state.
      * Allow multiple entries when described in different contexts.

    - Class: Phase separation driver type
      * "ps_self": Requires BOTH conditions:
        > Explicit in vitro reconstitution (purified protein)
        > Spontaneous droplet formation without co-factors
      * "ps_other": other phase separated proteins
  expected_output: >
    A json object of biomolecules that undergo phase separation with the following format:
    {
      "entities": [
        {
          "name": "gene name",
          "location": "exp. Cytoplasm",
          "MLO": "exp. P granule",
          "Material state": "liquid, hydrogel, solid",
          "Class": "ps_self, ps_other"
        }
      ]
    }
  agent: phase_separation_analyst


#Task4
data_consolidation_task:
  description: >
    Consolidate outputs from all analytical agents into a unified, structured JSON report:
    
    1. **Input Integration**:
       - Gene extraction data: {gene_extraction_task.output}
       - Validation results: {gene_validation_task.output}
       - Phase analysis data: {phase_analysis_task.output}
    
    2. **Conflict Resolution**:
       - Flag inconsistencies between localization and MLO types
       - Resolve conflicting evidence levels between agents
       - Prioritize direct experimental evidence over indirect
    
    3. **Data Normalization**:
       - Convert all organism names to scientific naming (e.g., "human" → "Homo sapiens")
       - Standardize molecule types: Protein → protein, dna → DNA, rna → RNA
       - Capitalize localization terms: Cytoplasm, Nucleus
       - Sanitize special characters in gene names (e.g., "NF-κB" → "NF-kB")
    
    4. **JSON Architecture**:
       - Build hierarchical structure with nested objects
       - Ensure all arrays use consistent formatting
       - Add metadata section for validation summaries

    Inputs:
    - Extraction results: {information_extraction_task.output}
    
    Output Requirements:
    - Must validate against JSON Schema version 2.0
    - Must be human-readable with proper indentation
    - Must include data provenance tracking

  expected_output: >
    Standardized JSON report format:
    {
      "entities": [
        {
          "core_identity": {
            "name": "FUS protein",
            "abbreviation": "FUS",
            "gene_name": "FUS",
            "organism": "Homo sapiens",
            "molecule_type": "protein"
          },
          "phase_properties": {
            "location": "Nucleus",
            "mlo": ["Stress Granules"],
            "material_state": "liquid",
            "driver_class": "ps_self"
          },
          "validation": {
            "evidence_types": ["in_vitro_droplets", "in_vivo_dynamic"]
          }
        }
      ]
    }
  agent: Data_Architect     