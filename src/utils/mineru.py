#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Simple PDF to Markdown Converter using Mineru API

This script converts a single specified PDF file to Markdown format using the Mineru API.
"""

import os
import sys
import time
import argparse
import requests
import zipfile
import dotenv
from typing import Optional


def convert_pdf_to_markdown(
    pdf_path: str, output_path: str, token: str, is_ocr: bool = False
) -> bool:
    """Convert a single PDF file to Markdown using Mineru API.

    Args:
        pdf_path: Path to the PDF file
        output_path: Path where the Markdown file should be saved
        token: Mineru API token
        is_ocr: Whether to use OCR

    Returns:
        True if successful, False otherwise
    """
    base_url = "https://mineru.net/api/v4"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}",
    }

    filename = os.path.basename(pdf_path)
    print(f"Converting {filename} to Markdown...")

    # Step 1: Create upload task
    url = f"{base_url}/file-urls/batch"
    data = {
        "enable_formula": True,
        "language": "en",
        "enable_table": True,
        "files": [{"name": filename, "is_ocr": is_ocr, "data_id": filename}],
    }

    try:
        print("Creating upload URL...")
        response = requests.post(url, headers=headers, json=data)

        if response.status_code != 200:
            print(f"Failed to create upload URL: {response.status_code}")
            return False

        result = response.json()
        if result["code"] != 0:
            print(f"API error: {result['msg']}")
            return False

        batch_id = result["data"]["batch_id"]
        file_url = result["data"]["file_urls"][0]

        # Step 2: Upload the PDF file
        print("Uploading PDF file...")
        with open(pdf_path, "rb") as f:
            upload_response = requests.put(file_url, data=f)

        if upload_response.status_code != 200:
            print(f"Failed to upload file: {upload_response.status_code}")
            return False

        print(f"File uploaded successfully. Batch ID: {batch_id}")

        # Step 3: Poll for completion
        status_url = f"{base_url}/extract-results/batch/{batch_id}"
        print("Waiting for conversion to complete...")

        while True:
            time.sleep(10)  # Wait 10 seconds between checks

            status_response = requests.get(status_url, headers=headers)
            if status_response.status_code != 200:
                print(f"Failed to check status: {status_response.status_code}")
                return False

            status_result = status_response.json()
            if status_result["code"] != 0:
                print(f"Status check error: {status_result['msg']}")
                return False

            extract_results = status_result["data"]["extract_result"]
            if not extract_results:
                print("No results yet, continuing to wait...")
                continue

            result_item = extract_results[0]
            state = result_item.get("state", "")

            if state == "done":
                download_url = result_item.get("full_zip_url")
                if not download_url:
                    print("No download URL found")
                    return False

                print(f"Conversion completed! Downloading result...\n{download_url}")
                break
            elif state == "failed":
                error_msg = result_item.get("err_msg", "Unknown error")
                print(f"Conversion failed: {error_msg}")
                return False
            else:
                # Show progress if available
                progress = result_item.get("extract_progress", {})
                if progress:
                    extracted = progress.get("extracted_pages", 0)
                    total = progress.get("total_pages", 0)
                    if total > 0:
                        print(f"Progress: {extracted}/{total} pages")
                else:
                    print(f"Status: {state}")

        # Step 4: Download and extract the result
        download_response = requests.get(download_url)
        if download_response.status_code != 200:
            print(f"Failed to download result: {download_response.status_code}")
            return False

        # Save to temporary zip file
        temp_zip = output_path + ".temp.zip"
        with open(temp_zip, "wb") as f:
            f.write(download_response.content)

        # Extract all files to the output directory
        try:
            with zipfile.ZipFile(temp_zip, "r") as zip_ref:
                # Extract all files to the output directory
                zip_ref.extractall(output_path)

            # Rename full.md to match the folder name (PDF basename)
            pdf_basename = os.path.splitext(os.path.basename(pdf_path))[0]
            full_md_path = os.path.join(output_path, "full.md")
            target_md_path = os.path.join(output_path, f"{pdf_basename}.md")

            if os.path.exists(full_md_path):
                os.rename(full_md_path, target_md_path)

            # Clean up temporary zip file
            os.remove(temp_zip)

            print(
                f"Successfully converted {filename} and extracted all files to {output_path}"
            )
            return True

        except Exception as e:
            print(f"Error extracting files: {e}")
            if os.path.exists(temp_zip):
                os.remove(temp_zip)
            return False

    except Exception as e:
        print(f"Error during conversion: {e}")
        return False


def main():
    """Simple command-line interface for PDF to Markdown conversion."""
    # Load environment variables from .env file
    dotenv.load_dotenv()

    parser = argparse.ArgumentParser(
        description="Convert a single PDF file to Markdown using Mineru API"
    )
    parser.add_argument("pdf_path", help="Path to the PDF file to convert")
    parser.add_argument(
        "output_path", help="Path where the Markdown file should be saved"
    )
    parser.add_argument(
        "--token", "-t", help="Mineru API token (overrides environment variable)"
    )
    parser.add_argument(
        "--ocr", action="store_true", help="Enable OCR for scanned documents"
    )

    args = parser.parse_args()

    # Get token from command line or environment variable
    token = args.token or os.environ.get("MINERU_API")

    if not token:
        print("Error: Mineru API token not provided.")
        print("Please specify it with --token or set MINERU_API in .env file.")
        sys.exit(1)

    # Check if PDF file exists
    if not os.path.exists(args.pdf_path):
        print(f"Error: PDF file not found: {args.pdf_path}")
        sys.exit(1)

    # Handle output path - create a folder named after the PDF file
    if os.path.isdir(args.output_path) or args.output_path.endswith("/"):
        # Remove trailing slash if present
        base_output_dir = args.output_path.rstrip("/")
        # Generate folder name based on input PDF name (without extension)
        pdf_basename = os.path.splitext(os.path.basename(args.pdf_path))[0]
        output_path = os.path.join(base_output_dir, pdf_basename)
    else:
        # If a specific path is given, use it as the extraction directory
        output_path = args.output_path

    # Create output directory if needed
    if not os.path.exists(output_path):
        os.makedirs(output_path)

    # Convert the PDF
    success = convert_pdf_to_markdown(args.pdf_path, output_path, token, args.ocr)

    if success:
        print("Conversion completed successfully!")
        sys.exit(0)
    else:
        print("Conversion failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
